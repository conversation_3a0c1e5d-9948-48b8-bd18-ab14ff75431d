"""
Test critical fixes: Gift code cancellation and button font change
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CriticalFixesTester:
    """Test critical fixes functionality"""
    
    async def test_gift_code_cancellation_fix(self):
        """Test gift code cancellation fix"""
        print("🔍 Testing gift code cancellation fix...")
        
        try:
            from handlers.session_handlers import SessionHandlers
            
            session_handlers = SessionHandlers()
            
            # Check if handle_cancel_command method exists and is fixed
            if hasattr(session_handlers, 'handle_cancel_command'):
                print("   ✅ handle_cancel_command method: Exists")
                
                # Check method source for problematic code
                import inspect
                source = inspect.getsource(session_handlers.handle_cancel_command)
                
                # Check if problematic BroadcastService calls are removed
                if 'get_active_broadcast' in source:
                    print("   ❌ Problematic code still present: get_active_broadcast")
                    return False
                else:
                    print("   ✅ Problematic code removed: get_active_broadcast")
                
                # Check if proper session handling is implemented
                if 'get_user_session' in source and 'gift_code' in source:
                    print("   ✅ Proper session handling: Implemented")
                else:
                    print("   ❌ Proper session handling: Missing")
                    return False
                
                # Check if specific cancellation messages are implemented
                cancellation_messages = [
                    'Gift Code Process Cancelled',
                    'Broadcast Process Cancelled',
                    'Balance Operation Cancelled',
                    'Withdrawal Process Cancelled'
                ]
                
                all_messages_present = True
                for msg in cancellation_messages:
                    if msg in source:
                        print(f"   ✅ Cancellation message present: {msg}")
                    else:
                        print(f"   ❌ Cancellation message missing: {msg}")
                        all_messages_present = False
                
                return all_messages_present
            else:
                print("   ❌ handle_cancel_command method: Missing")
                return False
                
        except Exception as e:
            print(f"   💥 Error: {e}")
            return False
    
    async def test_get_money_button_font_change(self):
        """Test GET MONEY button font change"""
        print("🔍 Testing GET MONEY button font change...")
        
        try:
            # Test 1: Check user_handlers.py button
            with open('handlers/user_handlers.py', 'r', encoding='utf-8') as f:
                user_handlers_content = f.read()

            if '𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔' in user_handlers_content:
                print("   ✅ User handlers button: Updated to new font")
            else:
                print("   ❌ User handlers button: Still using old font")
                return False

            # Test 2: Check force_subscription_service.py button
            with open('services/force_subscription_service.py', 'r', encoding='utf-8') as f:
                force_sub_content = f.read()

            if '𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔' in force_sub_content:
                print("   ✅ Force subscription service button: Updated to new font")
            else:
                print("   ❌ Force subscription service button: Still using old font")
                return False

            # Test 3: Check welcome message text in helpers.py
            with open('utils/helpers.py', 'r', encoding='utf-8') as f:
                helpers_content = f.read()

            if '𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔' in helpers_content:
                print("   ✅ Welcome message text: Updated to new font")
            else:
                print("   ❌ Welcome message text: Still using old font")
                return False

            # Test 4: Verify old font is completely removed from main files (excluding test files)
            old_font_pattern = '💰GET MONEY💰'
            old_font_count = user_handlers_content.count(old_font_pattern)
            old_font_count += force_sub_content.count(old_font_pattern)
            old_font_count += helpers_content.count(old_font_pattern)

            # Check main service files
            import os
            main_files = [
                'services/user_service.py',
                'services/referral_service.py',
                'handlers/callback_handlers.py',
                'utils/constants.py'
            ]

            for file_path in main_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        old_font_count += content.count(old_font_pattern)

            if old_font_count == 0:
                print("   ✅ Old font completely removed: No instances found in main files")
            else:
                print(f"   ❌ Old font still present: {old_font_count} instances found in main files")
                return False

            return True
                
        except Exception as e:
            print(f"   💥 Error: {e}")
            return False
    
    async def test_welcome_message_generation(self):
        """Test welcome message generation with new font"""
        print("🔍 Testing welcome message generation...")
        
        try:
            from utils.helpers import get_welcome_message
            
            # Test welcome message generation
            welcome_message = await get_welcome_message(50, "testchannel")
            
            print(f"   Generated welcome message:")
            print(f"   {welcome_message}")
            
            # Check if new font is used in the message
            if '𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔' in welcome_message:
                print("   ✅ Welcome message uses new font: Correct")
            else:
                print("   ❌ Welcome message uses old font: Incorrect")
                return False
            
            # Check if old font is not present
            old_font_pattern = '💰GET MONEY💰'
            if old_font_pattern in welcome_message:
                print("   ❌ Welcome message contains old font: Should be removed")
                return False
            else:
                print("   ✅ Welcome message doesn't contain old font: Correct")
            
            return True
                
        except Exception as e:
            print(f"   💥 Error: {e}")
            return False
    
    async def test_button_functionality_preservation(self):
        """Test that button functionality is preserved"""
        print("🔍 Testing button functionality preservation...")
        
        try:
            from handlers.user_handlers import UserHandlers
            
            user_handlers = UserHandlers()
            
            # Check if handle_start_command method still creates the button correctly
            import inspect
            source = inspect.getsource(user_handlers.handle_start_command)
            
            # Check if callback_data is still 'joined'
            if "callback_data='joined'" in source:
                print("   ✅ Button callback data preserved: 'joined'")
            else:
                print("   ❌ Button callback data changed: Should be 'joined'")
                return False
            
            # Check if InlineKeyboardButton is still used
            if 'InlineKeyboardButton' in source:
                print("   ✅ Button creation method preserved: InlineKeyboardButton")
            else:
                print("   ❌ Button creation method changed: Should use InlineKeyboardButton")
                return False
            
            return True
                
        except Exception as e:
            print(f"   💥 Error: {e}")
            return False
    
    async def test_session_cancellation_scenarios(self):
        """Test different session cancellation scenarios"""
        print("🔍 Testing session cancellation scenarios...")
        
        try:
            from handlers.session_handlers import SessionHandlers
            
            session_handlers = SessionHandlers()
            
            # Test session setting and cancellation
            test_user_id = 99999
            
            # Test different session types
            session_types = [
                'gift_code_claim',
                'broadcast_text',
                'add_balance_id',
                'withdrawal_request',
                'add_force_channel_forward'
            ]
            
            all_sessions_work = True
            
            for session_type in session_types:
                try:
                    # Set session
                    await session_handlers.set_user_session(test_user_id, session_type)
                    
                    # Get session to verify it was set
                    session = await session_handlers.get_user_session(test_user_id)
                    
                    if session and session.get('step') == session_type:
                        print(f"   ✅ Session type '{session_type}': Can be set and retrieved")
                        
                        # Clear session
                        await session_handlers.clear_user_session(test_user_id)
                        
                        # Verify session is cleared
                        cleared_session = await session_handlers.get_user_session(test_user_id)
                        if not cleared_session:
                            print(f"   ✅ Session type '{session_type}': Can be cleared")
                        else:
                            print(f"   ❌ Session type '{session_type}': Failed to clear")
                            all_sessions_work = False
                    else:
                        print(f"   ❌ Session type '{session_type}': Failed to set")
                        all_sessions_work = False
                        
                except Exception as e:
                    print(f"   ❌ Session type '{session_type}': Error - {e}")
                    all_sessions_work = False
            
            return all_sessions_work
                
        except Exception as e:
            print(f"   💥 Error: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive test of critical fixes"""
        print("🔍 CRITICAL FIXES VERIFICATION TEST")
        print("=" * 60)
        
        tests = [
            ("Gift Code Cancellation Fix", self.test_gift_code_cancellation_fix),
            ("GET MONEY Button Font Change", self.test_get_money_button_font_change),
            ("Welcome Message Generation", self.test_welcome_message_generation),
            ("Button Functionality Preservation", self.test_button_functionality_preservation),
            ("Session Cancellation Scenarios", self.test_session_cancellation_scenarios)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"\n{status}: {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"\n💥 ERROR: {test_name} - {e}")
        
        # Summary
        print(f"\n" + "=" * 60)
        print(f"📊 CRITICAL FIXES TEST SUMMARY")
        print(f"=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")
        
        print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Gift code cancellation error fixed")
            print("✅ GET MONEY button font changed successfully")
            print("✅ All functionality preserved")
            print("✅ Session handling improved")
            return True
        else:
            print("⚠️  Some tests failed")
            print("🔧 Check the failed tests above for details")
            return False

async def main():
    """Main test function"""
    tester = CriticalFixesTester()
    
    try:
        success = await tester.run_comprehensive_test()
        
        if success:
            print("\n🚀 CRITICAL FIXES VERIFIED!")
            print("\n📋 FIXED ISSUES:")
            print("• Gift code cancellation error - ✅ FIXED")
            print("• GET MONEY button font change - ✅ IMPLEMENTED")
            print("• Session handling improvements - ✅ ENHANCED")
            print("• Error message specificity - ✅ IMPROVED")
            print("\n🎯 Both critical issues resolved successfully!")
        else:
            print("\n❌ Critical fixes verification FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
