<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON>gin - Telegram <PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 400px;
            width: 100%;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 32px;
        }
        
        .form-control {
            border-radius: 15px;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 15px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .security-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #6c757d;
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-robot"></i>
            </div>
            <h2 class="mb-2">Admin Login</h2>
            <p class="text-muted">Telegram Bot Management Panel - Optimized</p>
            <small class="text-success">
                <i class="fas fa-users me-1"></i>
                Managing <?php echo number_format(getUserCount()); ?> users
            </small>
        </div>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST" action="index.php?action=login">
            <div class="mb-4">
                <label for="login_code" class="form-label">
                    <i class="fas fa-key me-2"></i>Access Code
                </label>
                <input 
                    type="password" 
                    class="form-control" 
                    id="login_code" 
                    name="login_code" 
                    placeholder="Enter your access code"
                    required
                    autocomplete="current-password"
                    maxlength="10"
                >
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <i class="fas fa-sign-in-alt me-2"></i>
                Access Admin Panel
            </button>
        </form>
        
        <div class="security-info">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-shield-alt me-2 text-success"></i>
                <strong>Security Features</strong>
            </div>
            <ul class="mb-0 ps-3">
                <li>Secure session management</li>
                <li>Auto-logout after 1 hour</li>
                <li>Access logging enabled</li>
                <li>SSL encryption protected</li>
            </ul>
        </div>
        
        <div class="footer-text">
            <p class="mb-1">
                <i class="fas fa-clock me-1"></i>
                <?php echo date('M d, Y H:i'); ?>
            </p>
            <p class="mb-0">
                <i class="fas fa-server me-1"></i>
                Powered by PHP • Hosted on Hostinger
            </p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced login form handling
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const btn = document.getElementById('loginBtn');
            const codeInput = document.getElementById('login_code');

            // Auto-focus on code input
            codeInput.focus();

            // Form submission handling
            form.addEventListener('submit', function(e) {
                const code = codeInput.value.trim();

                // Basic validation
                if (code.length < 3) {
                    e.preventDefault();
                    alert('Please enter a valid access code.');
                    codeInput.focus();
                    return;
                }

                // Show loading state
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Authenticating...';

                // Timeout to re-enable button if something goes wrong
                setTimeout(() => {
                    if (btn.disabled) {
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Access Admin Panel';
                        alert('Login timeout. Please try again.');
                    }
                }, 15000); // 15 seconds timeout
            });

            // Handle Enter key
            codeInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    form.submit();
                }
            });
        });
    </script>

    <script>
        // Focus on login code input
        document.getElementById('login_code').focus();
        
        // Add some interactive effects
        document.querySelector('.btn-login').addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        document.querySelector('.btn-login').addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const loginCode = document.getElementById('login_code').value;
            
            if (!loginCode || loginCode.length < 3) {
                e.preventDefault();
                alert('Please enter a valid access code');
                return false;
            }
        });
        
        // Clear any error messages after 5 seconds
        const errorAlert = document.querySelector('.alert-danger');
        if (errorAlert) {
            setTimeout(function() {
                errorAlert.style.opacity = '0';
                setTimeout(function() {
                    errorAlert.remove();
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>
