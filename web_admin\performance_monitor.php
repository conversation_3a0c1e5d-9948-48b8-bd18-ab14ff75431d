<?php
/**
 * Performance Monitor for Web Admin Panel
 * Helps identify performance bottlenecks and system health
 */

require_once 'config.php';

// Require authentication
requireAuth();

// Performance monitoring functions
function getSystemInfo() {
    return [
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
    ];
}

function getFileSystemInfo() {
    $info = [];
    
    // Users file info
    if (file_exists(USERS_FILE)) {
        $info['users_file'] = [
            'size' => filesize(USERS_FILE),
            'size_mb' => round(filesize(USERS_FILE) / 1024 / 1024, 2),
            'modified' => filemtime(USERS_FILE),
            'readable' => is_readable(USERS_FILE),
            'writable' => is_writable(USERS_FILE)
        ];
    }
    
    // Data directory info
    if (is_dir('../data')) {
        $info['data_directory'] = [
            'readable' => is_readable('../data'),
            'writable' => is_writable('../data'),
            'files_count' => count(glob('../data/*'))
        ];
    }
    
    return $info;
}

function performanceTest() {
    $results = [];
    
    // Test 1: JSON file read speed
    $start = microtime(true);
    if (STORAGE_MODE === 'json') {
        $users = readJsonFile(USERS_FILE);
        $userCount = count($users);
    } else {
        $userCount = getUserCount();
    }
    $end = microtime(true);
    
    $results['json_read'] = [
        'time' => round(($end - $start) * 1000, 2), // milliseconds
        'user_count' => $userCount,
        'status' => ($end - $start) < 2 ? 'Good' : (($end - $start) < 5 ? 'Warning' : 'Critical')
    ];
    
    // Test 2: Pagination performance
    $start = microtime(true);
    $paginated = getUsersPaginated(1, 50, '', 'all', 'all');
    $end = microtime(true);
    
    $results['pagination'] = [
        'time' => round(($end - $start) * 1000, 2),
        'users_loaded' => count($paginated['users']),
        'status' => ($end - $start) < 1 ? 'Good' : (($end - $start) < 3 ? 'Warning' : 'Critical')
    ];
    
    // Test 3: Statistics calculation
    $start = microtime(true);
    $stats = getBotStatistics();
    $end = microtime(true);
    
    $results['statistics'] = [
        'time' => round(($end - $start) * 1000, 2),
        'cached' => file_exists('../data/admin_stats_cache.json'),
        'status' => ($end - $start) < 1 ? 'Good' : (($end - $start) < 3 ? 'Warning' : 'Critical')
    ];
    
    return $results;
}

// Get performance data
$systemInfo = getSystemInfo();
$fileSystemInfo = getFileSystemInfo();
$performanceResults = performanceTest();

// Calculate overall health score
$healthScore = 100;
foreach ($performanceResults as $test) {
    if ($test['status'] === 'Warning') $healthScore -= 10;
    if ($test['status'] === 'Critical') $healthScore -= 25;
}

$healthStatus = $healthScore >= 80 ? 'Excellent' : ($healthScore >= 60 ? 'Good' : ($healthScore >= 40 ? 'Warning' : 'Critical'));
$healthColor = $healthScore >= 80 ? 'success' : ($healthScore >= 60 ? 'info' : ($healthScore >= 40 ? 'warning' : 'danger'));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Monitor - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .performance-card {
            transition: transform 0.2s;
        }
        .performance-card:hover {
            transform: translateY(-2px);
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>Performance Monitor</h2>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Back
                        </a>
                    </div>
                </div>
                
                <!-- Health Score -->
                <div class="row mb-4">
                    <div class="col-md-4 mx-auto">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>System Health</h5>
                                <div class="display-4 text-<?php echo $healthColor; ?>"><?php echo $healthScore; ?>%</div>
                                <p class="text-<?php echo $healthColor; ?> mb-0"><?php echo $healthStatus; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Performance Tests -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5>Performance Tests</h5>
                    </div>
                    <?php foreach ($performanceResults as $testName => $result): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card performance-card">
                            <div class="card-body">
                                <h6 class="card-title text-capitalize"><?php echo str_replace('_', ' ', $testName); ?></h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 mb-0"><?php echo $result['time']; ?>ms</span>
                                    <span class="badge bg-<?php echo $result['status'] === 'Good' ? 'success' : ($result['status'] === 'Warning' ? 'warning' : 'danger'); ?>">
                                        <?php echo $result['status']; ?>
                                    </span>
                                </div>
                                <?php if (isset($result['user_count'])): ?>
                                <small class="text-muted">Users: <?php echo number_format($result['user_count']); ?></small>
                                <?php endif; ?>
                                <?php if (isset($result['cached']) && $result['cached']): ?>
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Cached</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- System Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-server me-2"></i>System Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td>PHP Version</td>
                                        <td><?php echo $systemInfo['php_version']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Memory Limit</td>
                                        <td><?php echo $systemInfo['memory_limit']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Max Execution Time</td>
                                        <td><?php echo $systemInfo['max_execution_time']; ?>s</td>
                                    </tr>
                                    <tr>
                                        <td>Current Memory Usage</td>
                                        <td><?php echo round($systemInfo['memory_usage'] / 1024 / 1024, 2); ?> MB</td>
                                    </tr>
                                    <tr>
                                        <td>Peak Memory Usage</td>
                                        <td><?php echo round($systemInfo['memory_peak'] / 1024 / 1024, 2); ?> MB</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-folder me-2"></i>File System</h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($fileSystemInfo['users_file'])): ?>
                                <h6>Users File</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>Size</td>
                                        <td><?php echo $fileSystemInfo['users_file']['size_mb']; ?> MB</td>
                                    </tr>
                                    <tr>
                                        <td>Last Modified</td>
                                        <td><?php echo date('M d, Y H:i', $fileSystemInfo['users_file']['modified']); ?></td>
                                    </tr>
                                    <tr>
                                        <td>Permissions</td>
                                        <td>
                                            <?php if ($fileSystemInfo['users_file']['readable']): ?>
                                                <span class="badge bg-success">R</span>
                                            <?php endif; ?>
                                            <?php if ($fileSystemInfo['users_file']['writable']): ?>
                                                <span class="badge bg-success">W</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                                <?php endif; ?>
                                
                                <?php if (isset($fileSystemInfo['data_directory'])): ?>
                                <h6>Data Directory</h6>
                                <p class="mb-0">
                                    Files: <?php echo $fileSystemInfo['data_directory']['files_count']; ?>
                                    <?php if ($fileSystemInfo['data_directory']['writable']): ?>
                                        <span class="badge bg-success ms-2">Writable</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger ms-2">Not Writable</span>
                                    <?php endif; ?>
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recommendations -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Performance Recommendations</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <?php if ($performanceResults['json_read']['status'] !== 'Good'): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                        Consider migrating to MySQL for better performance with large datasets
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php if (!$performanceResults['statistics']['cached']): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-info-circle text-info me-2"></i>
                                        Statistics caching is working - data refreshes every 5 minutes
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php if ($systemInfo['memory_usage'] > 100 * 1024 * 1024): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-memory text-warning me-2"></i>
                                        High memory usage detected - consider optimizing data loading
                                    </li>
                                    <?php endif; ?>
                                    
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Pagination is active - loading <?php echo WEB_ADMIN_USERS_PER_PAGE; ?> users per page
                                    </li>
                                    
                                    <li class="mb-0">
                                        <i class="fas fa-tools text-info me-2"></i>
                                        Use the <a href="clear_cache.php">Cache Management</a> tool to optimize performance
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
