"""
Constants and message templates
Maintains identical messages to PHP version
"""

from config.settings import settings

# Bot Messages (matching PHP version exactly)
BANNED_TEXT = settings.BANNED_TEXT
MAINTENANCE_TEXT = settings.MAINTENANCE_TEXT

# Welcome message template
WELCOME_MESSAGE_TEMPLATE = settings.WELCOME_MESSAGE_TEMPLATE

# Invitation message template  
INVITATION_MESSAGE_TEMPLATE = settings.INVITATION_MESSAGE_TEMPLATE

# Admin messages
ADMIN_ACCESS_DENIED = "❌ <b>Access Denied</b>\n\nThis command is only available to administrators."

# User status messages
USER_NOT_FOUND = "❌ <b>User Not Found</b>\n\nUser ID does not exist in the system."
INVALID_USER_ID = "❌ <b>Invalid User ID</b>\n\nPlease provide a valid numeric user ID."

# Rate limit messages
RATE_LIMIT_EXCEEDED = "⚠️ Too many requests. Please wait a moment before trying again."

# Process messages
PROCESS_CANCELLED = "🚫 Process cancelled."
INVALID_AMOUNT = "❌ <b>Invalid Amount</b>\n\nPlease enter a valid numeric amount."

# Withdrawal messages
WITHDRAWAL_SUCCESS = """✅ <b>Withdrawal Request Submitted!</b>

💵 <b>Amount:</b> ₹{amount}
⏰ <b>Date:</b> {date}

🔄 Your withdrawal request is now under review.
📧 You will be notified once it's processed.
💬 For any queries, contact our support team."""

WITHDRAWAL_APPROVED = """✅ <b>Withdrawal Approved!</b>

💵 <b>Amount:</b> ₹{amount}
⏰ <b>Date:</b> {date}

🎉 Your withdrawal request has been approved!
💳 The payment will be credited to your account in <b>1-2 working days</b>.

📧 You will receive a confirmation email once the payment is processed.
💬 For any queries, contact our support team."""

WITHDRAWAL_REJECTED = """❌ <b>Withdrawal Rejected</b>

💵 <b>Amount:</b> ₹{amount}
⏰ <b>Date:</b> {date}

😔 Your withdrawal request has been declined.

📋 <b>Possible reasons:</b>
• Incorrect account details
• Insufficient verification
• Policy violation
• Technical issues

💬 Please contact our support team for more information.
🔄 You can try submitting a new withdrawal request after resolving any issues."""

# Task messages
TASK_SUBMITTED = """✅ <b>Task Submission Received!</b>

📝 <b>Task:</b> {task_name}
💰 <b>Reward:</b> ₹{reward}

🔄 Your submission is now under review.
📧 You will be notified once it's processed."""

TASK_APPROVED = """✅ <b>Task Approved!</b>

📝 <b>Task:</b> {task_name}
💰 <b>Reward:</b> ₹{reward}

🎉 Your task submission has been approved!
💳 ₹{reward} has been added to your balance."""

TASK_REJECTED = """❌ <b>Task Rejected</b>

📝 <b>Task:</b> {task_name}
💰 <b>Reward:</b> ₹{reward}

😔 Your task submission has been rejected.
💬 Please contact our support team for more information."""

# Referral messages (matching PHP exactly)
REFERRAL_NOTIFICATION = """👏You invited <b>{name}</b>!
<blockquote><b>You Will Receive Money, After He/She Joins Channel & Clicks GET MONEY</b></blockquote>"""

# PHP uses dynamic message format in the service, not a constant template
REFERRAL_REWARD = """🎉Invite {name} 🔍  successfully! You get <b>₹{amount}! 🎁

💵Balance:₹{balance}.00</b>"""

# Level bonus messages
LEVEL_BONUS_CLAIMED = """🎉 <b>Level Bonus Claimed!</b>

🏆 <b>Level:</b> {level}
💰 <b>Bonus:</b> ₹{amount}

💳 ₹{amount} has been added to your balance!"""

# Gift code messages
GIFT_CODE_REDEEMED = """🎁 <b>Gift Code Redeemed!</b>

🎫 <b>Code:</b> {code}
💰 <b>Amount:</b> ₹{amount}

💳 ₹{amount} has been added to your balance!"""

GIFT_CODE_INVALID = "❌ <b>Invalid Gift Code</b>\n\nThe gift code you entered is invalid or has already been used."

# Broadcast messages
BROADCAST_STARTED = """📢 <b>Broadcast Started</b>

🆔 <b>Broadcast ID:</b> <code>{broadcast_id}</code>
👥 <b>Target Users:</b> {user_count}
📝 <b>Type:</b> {broadcast_type}

🔄 Broadcasting in progress..."""

BROADCAST_COMPLETED = """✅ <b>Broadcast Completed</b>

🆔 <b>Broadcast ID:</b> <code>{broadcast_id}</code>
✅ <b>Successful:</b> {success_count}
❌ <b>Failed:</b> {failed_count}
👥 <b>Total:</b> {total_count}

⏱️ <b>Duration:</b> {duration}"""

BROADCAST_CANCELLED = """🚫 <b>Broadcast Cancelled</b>

🆔 <b>Broadcast ID:</b> <code>{broadcast_id}</code>

The broadcast has been stopped and will complete processing current users."""

# Error messages
GENERIC_ERROR = "❌ <b>Error</b>\n\nSomething went wrong. Please try again later."
DATABASE_ERROR = "❌ <b>Database Error</b>\n\nFailed to process your request. Please try again later."
NETWORK_ERROR = "❌ <b>Network Error</b>\n\nFailed to connect to external services. Please try again later."

# Success messages
OPERATION_SUCCESS = "✅ Operation completed successfully!"
SETTINGS_UPDATED = "✅ <b>Settings Updated</b>\n\nYour changes have been saved successfully."

# Validation messages
INVALID_INPUT = "❌ <b>Invalid Input</b>\n\nPlease provide valid information."
REQUIRED_FIELD = "❌ <b>Required Field</b>\n\nThis field is required and cannot be empty."

# Channel messages
CHANNEL_JOIN_REQUIRED = "💡You Must <a href=\"https://t.me/{channel}\">Join Our Channel</a> Before Clicking On [💰GET MONEY💰]"
CHANNEL_NOT_JOINED = "❌ <b>Channel Not Joined</b>\n\nPlease join our channel first before proceeding."

# Account info messages
ACCOUNT_INFO_REQUIRED = """❌ <b>Account Information Required</b>

Please set up your account information first:
• Full Name
• Account Number
• IFSC Code
• Email Address
• Mobile Number

Use the 'Set Account Info' button to complete your profile."""

ACCOUNT_INFO_INCOMPLETE = """❌ <b>Incomplete Account Information</b>

Please complete all required fields:
{missing_fields}

Use the 'Set Account Info' button to update your profile."""

# OTP messages
OTP_SENT = """📱 <b>OTP Sent</b>

A verification code has been sent to your mobile number: {mobile}

Please enter the 6-digit code to verify your account."""

OTP_VERIFIED = """✅ <b>Mobile Number Verified</b>

Your mobile number has been successfully verified!"""

OTP_INVALID = "❌ <b>Invalid OTP</b>\n\nThe verification code you entered is incorrect. Please try again."

OTP_EXPIRED = "❌ <b>OTP Expired</b>\n\nThe verification code has expired. Please request a new one."

# Rank emoji mapping (matching PHP version)
RANK_EMOJIS = {
    1: "🥇",
    2: "🥈", 
    3: "🥉",
    4: "🏅",
    5: "🏅"
}

# Default rank emoji for positions > 5
DEFAULT_RANK_EMOJI = "🔸"

# File type constants
ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/jpg']
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# Session timeout (in seconds)
SESSION_TIMEOUT = 3600  # 1 hour

# Rate limiting
DEFAULT_RATE_LIMIT = 30  # requests per minute
ADMIN_RATE_LIMIT = 100   # requests per minute for admins
