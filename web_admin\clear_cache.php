<?php
/**
 * Cache Clearing Utility for Web Admin Panel
 * Helps clear cached data to improve performance
 */

require_once 'config.php';

// Require authentication
requireAuth();

$message = '';
$messageType = 'info';

// Handle cache clearing actions
if ($_POST['action'] ?? false) {
    try {
        switch ($_POST['action']) {
            case 'clear_stats_cache':
                $cacheFile = '../data/admin_stats_cache.json';
                if (file_exists($cacheFile)) {
                    unlink($cacheFile);
                    $message = 'Statistics cache cleared successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'No statistics cache found.';
                    $messageType = 'info';
                }
                break;
                
            case 'clear_all_cache':
                $cacheFiles = [
                    '../data/admin_stats_cache.json',
                    '../data/temp_export.csv'
                ];
                
                $cleared = 0;
                foreach ($cacheFiles as $file) {
                    if (file_exists($file)) {
                        unlink($file);
                        $cleared++;
                    }
                }
                
                $message = "Cleared {$cleared} cache files successfully!";
                $messageType = 'success';
                break;
                
            case 'optimize_json':
                // Optimize JSON files by removing empty entries and compacting
                if (STORAGE_MODE === 'json') {
                    $usersData = readJsonFile(USERS_FILE);
                    $optimized = 0;
                    
                    // Remove any null or empty entries
                    foreach ($usersData as $userId => $userData) {
                        if (empty($userData) || !is_array($userData)) {
                            unset($usersData[$userId]);
                            $optimized++;
                        }
                    }
                    
                    if ($optimized > 0) {
                        writeJsonFile(USERS_FILE, $usersData);
                        $message = "Optimized JSON file - removed {$optimized} empty entries.";
                        $messageType = 'success';
                    } else {
                        $message = "JSON file is already optimized.";
                        $messageType = 'info';
                    }
                } else {
                    $message = "JSON optimization is only available in JSON storage mode.";
                    $messageType = 'warning';
                }
                break;
                
            default:
                $message = 'Invalid action.';
                $messageType = 'danger';
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get cache information
$cacheInfo = [];
$cacheFiles = [
    'Statistics Cache' => '../data/admin_stats_cache.json',
    'Temp Export' => '../data/temp_export.csv'
];

foreach ($cacheFiles as $name => $file) {
    if (file_exists($file)) {
        $cacheInfo[$name] = [
            'exists' => true,
            'size' => filesize($file),
            'modified' => filemtime($file)
        ];
    } else {
        $cacheInfo[$name] = ['exists' => false];
    }
}

// Get storage information
$storageInfo = [
    'mode' => STORAGE_MODE,
    'users_file_size' => file_exists(USERS_FILE) ? filesize(USERS_FILE) : 0,
    'total_users' => getUserCount()
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Management - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-broom me-2"></i>Cache Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Storage Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>Storage Mode</h6>
                                        <h4 class="text-primary"><?php echo strtoupper($storageInfo['mode']); ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>Total Users</h6>
                                        <h4 class="text-success"><?php echo number_format($storageInfo['total_users']); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cache Information -->
                        <h6>Cache Files Status</h6>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Cache Type</th>
                                        <th>Status</th>
                                        <th>Size</th>
                                        <th>Last Modified</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cacheInfo as $name => $info): ?>
                                    <tr>
                                        <td><?php echo $name; ?></td>
                                        <td>
                                            <?php if ($info['exists']): ?>
                                                <span class="badge bg-success">Exists</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Not Found</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $info['exists'] ? number_format($info['size']) . ' bytes' : '-'; ?>
                                        </td>
                                        <td>
                                            <?php echo $info['exists'] ? date('M d, Y H:i', $info['modified']) : '-'; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Cache Actions -->
                        <h6>Cache Actions</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <form method="POST" class="d-grid">
                                    <input type="hidden" name="action" value="clear_stats_cache">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-chart-bar me-1"></i>Clear Stats Cache
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4 mb-3">
                                <form method="POST" class="d-grid">
                                    <input type="hidden" name="action" value="clear_all_cache">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('Clear all cache files?')">
                                        <i class="fas fa-trash me-1"></i>Clear All Cache
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4 mb-3">
                                <form method="POST" class="d-grid">
                                    <input type="hidden" name="action" value="optimize_json">
                                    <button type="submit" class="btn btn-info" onclick="return confirm('Optimize JSON files? This will remove empty entries.')">
                                        <i class="fas fa-compress me-1"></i>Optimize JSON
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Admin Panel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
