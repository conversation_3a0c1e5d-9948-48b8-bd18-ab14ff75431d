"""
Standalone test for Level Rewards Models
Tests only the model functionality without database dependencies
"""

import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [123456789]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date
})
sys.modules['utils.helpers'] = helpers_module

# Mock constants
constants_module = type('MockConstants', (), {
    'RANK_EMOJIS': {},
    'DEFAULT_RANK_EMOJI': '🏆',
    'BANNED_TEXT': 'You are banned',
    'MAINTENANCE_TEXT': 'Under maintenance'
})
sys.modules['utils.constants'] = constants_module

from models.level_rewards import LevelRewardsModel

def test_level_rewards_models():
    """Test level rewards model functionality"""
    print("Testing level rewards models...")
    
    # Test default configuration
    default_config = LevelRewardsModel.get_default_config()
    assert 'referral_requirements' in default_config, "Default config missing referral_requirements"
    assert 'bonus_amounts' in default_config, "Default config missing bonus_amounts"
    assert len(default_config['referral_requirements']) == 6, "Should have 6 referral requirements"
    assert len(default_config['bonus_amounts']) == 6, "Should have 6 bonus amounts"
    print("✅ Default configuration test passed")
    
    # Test configuration validation
    valid_config = {
        'referral_requirements': [1, 5, 10, 15, 20, 25],
        'bonus_amounts': [2, 10, 15, 20, 25, 30]
    }
    validation = LevelRewardsModel.validate_config(valid_config)
    assert validation['valid'], f"Valid config should pass validation: {validation['errors']}"
    print("✅ Valid configuration validation test passed")
    
    # Test invalid configuration
    invalid_config = {
        'referral_requirements': [1, 5, 10],  # Only 3 levels
        'bonus_amounts': [2, 10, 15, 20, 25, 30]
    }
    validation = LevelRewardsModel.validate_config(invalid_config)
    assert not validation['valid'], "Invalid config should fail validation"
    print("✅ Invalid configuration validation test passed")
    
    # Test level calculations
    referral_count = 12
    current_level = LevelRewardsModel.calculate_user_current_level(referral_count, default_config)
    assert current_level == 3, f"User with 12 referrals should be level 3, got {current_level}"
    print("✅ Level calculation test passed")
    
    # Test eligible levels
    claimed_levels = [1, 2]
    eligible_levels = LevelRewardsModel.get_user_eligible_levels(
        referral_count, claimed_levels, default_config
    )
    assert 3 in eligible_levels, "Level 3 should be eligible"
    assert 1 not in eligible_levels, "Level 1 should not be eligible (already claimed)"
    print("✅ Eligible levels test passed")
    
    # Test next level info
    next_level_info = LevelRewardsModel.get_next_level_info(referral_count, current_level, default_config)
    assert next_level_info is not None, "Should have next level info"
    assert next_level_info['level'] == 4, "Next level should be 4"
    assert next_level_info['remaining_referrals'] == 3, "Should need 3 more referrals (15-12)"
    print("✅ Next level info test passed")
    
    # Test max level scenario
    max_level_info = LevelRewardsModel.get_next_level_info(30, 6, default_config)
    assert max_level_info is None, "Should return None for max level"
    print("✅ Max level test passed")
    
    print("✅ All level rewards models tests passed!")

def test_message_formatting():
    """Test message formatting"""
    print("Testing message formatting...")
    
    # Test level rewards message
    config = LevelRewardsModel.get_default_config()
    message = LevelRewardsModel.format_level_rewards_message(
        user_id=123,
        referral_count=12,
        current_level=3,
        claimed_levels=[1, 2],
        eligible_levels=[3],
        next_level_info={'level': 4, 'remaining_referrals': 3, 'bonus_amount': 20},
        config=config
    )
    
    print(f"Generated message:\n{message}")  # Debug output
    assert "Level Rewards System" in message, "Should contain title"
    assert "12" in message, "Should contain referral count"
    assert "Level 3" in message, "Should contain current level"
    assert "All Levels:" in message, "Should contain all levels section"
    assert "Next Level Progress:" in message, "Should contain next level progress"
    assert "🎁 Ready to Claim" in message, "Should show eligible level"
    assert "✅ Claimed" in message, "Should show claimed levels"
    print("✅ Level rewards message test passed")
    
    # Test success message
    success_message = LevelRewardsModel.format_level_claim_success_message(3, 15.0, 115.0)
    print(f"Success message:\n{success_message}")  # Debug output
    assert "Level Bonus Claimed Successfully!" in success_message, "Should contain success title"
    assert "3" in success_message, "Should contain level"
    assert "₹15" in success_message, "Should contain amount"
    print("✅ Success message test passed")
    
    # Test admin config message
    admin_message = LevelRewardsModel.format_admin_config_message(config, True)
    assert "Level Rewards Configuration" in admin_message, "Should contain title"
    assert "✅ Enabled" in admin_message, "Should show enabled status"
    assert "Current Configuration:" in admin_message, "Should contain configuration"
    print("✅ Admin config message test passed")
    
    # Test config update message
    update_message = LevelRewardsModel.format_config_update_success_message(
        [2, 8, 15, 25, 35, 50], [5, 15, 25, 35, 50, 75]
    )
    assert "Configuration Updated!" in update_message, "Should contain update title"
    assert "New Configuration:" in update_message, "Should contain new config"
    print("✅ Config update message test passed")
    
    # Test toggle message
    toggle_message = LevelRewardsModel.format_toggle_success_message(False, config)
    assert "System Updated!" in toggle_message, "Should contain update title"
    assert "❌ Disabled" in toggle_message, "Should show disabled status"
    assert "disabled" in toggle_message, "Should mention disabled action"
    print("✅ Toggle message test passed")
    
    print("✅ All message formatting tests passed!")

def test_keyboard_generation():
    """Test keyboard generation"""
    print("Testing keyboard generation...")
    
    # Mock telegram classes
    class MockInlineKeyboardButton:
        def __init__(self, text, callback_data):
            self.text = text
            self.callback_data = callback_data
    
    class MockInlineKeyboardMarkup:
        def __init__(self, keyboard):
            self.inline_keyboard = keyboard
    
    # Monkey patch telegram imports
    import sys
    telegram_module = type('MockTelegram', (), {
        'InlineKeyboardButton': MockInlineKeyboardButton,
        'InlineKeyboardMarkup': MockInlineKeyboardMarkup
    })
    sys.modules['telegram'] = telegram_module
    
    # Re-import the model to get the updated telegram classes
    import importlib
    import models.level_rewards
    importlib.reload(models.level_rewards)
    from models.level_rewards import LevelRewardsModel
    
    # Test level rewards keyboard
    config = LevelRewardsModel.get_default_config()
    eligible_levels = [1, 2]
    keyboard = LevelRewardsModel.create_level_rewards_keyboard(eligible_levels, config)
    
    assert keyboard is not None, "Should generate keyboard"
    assert len(keyboard.inline_keyboard) >= 1, "Should have at least back button"
    
    # Check for claim button
    claim_button_found = False
    for row in keyboard.inline_keyboard:
        for button in row:
            if "Claim Level" in button.text:
                claim_button_found = True
                break
    assert claim_button_found, "Should have claim button for eligible levels"
    print("✅ Level rewards keyboard test passed")
    
    # Test admin config keyboard
    admin_keyboard = LevelRewardsModel.create_admin_config_keyboard(True)
    assert admin_keyboard is not None, "Should generate admin keyboard"
    assert len(admin_keyboard.inline_keyboard) == 3, "Should have 3 rows"
    
    # Check button texts
    button_texts = []
    for row in admin_keyboard.inline_keyboard:
        for button in row:
            button_texts.append(button.text)
    
    assert any("Configure Levels" in text for text in button_texts), "Should have configure button"
    assert any("Disable" in text for text in button_texts), "Should have disable button"
    assert any("Back to Admin Panel" in text for text in button_texts), "Should have back button"
    print("✅ Admin config keyboard test passed")
    
    # Test claim success keyboard
    success_keyboard = LevelRewardsModel.create_claim_success_keyboard()
    assert success_keyboard is not None, "Should generate success keyboard"
    assert len(success_keyboard.inline_keyboard) == 2, "Should have 2 rows"
    print("✅ Claim success keyboard test passed")
    
    print("✅ All keyboard generation tests passed!")

def main():
    """Main test function"""
    try:
        print("🧪 Starting Level Rewards Models Tests...")
        print("=" * 50)
        
        test_level_rewards_models()
        print()
        test_message_formatting()
        print()
        test_keyboard_generation()
        
        print("=" * 50)
        print("🎉 All Level Rewards Models tests PASSED!")
        print("The level rewards models are working correctly.")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
