"""
Gift Code service for managing gift code generation and redemption
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.gift_code import GiftCodeModel
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class GiftCodeService:
    """Service for gift code operations"""
    
    def __init__(self):
        pass
    
    async def get_all_gift_codes(self) -> List[Dict[str, Any]]:
        """Get all gift codes (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            cursor = collection.find({})
            gift_codes = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for code in gift_codes:
                if '_id' in code:
                    del code['_id']
            
            return gift_codes
            
        except Exception as e:
            logger.error(f"Error getting all gift codes: {e}")
            return []
    
    async def add_gift_code(self, code_data: Dict[str, Any]) -> bool:
        """Add gift code to database (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            # Check if code already exists (case-insensitive)
            existing_code = await collection.find_one({
                "code": {"$regex": f"^{code_data['code']}$", "$options": "i"}
            })
            
            if existing_code:
                return False  # Code already exists
            
            # Add timestamps and initial values
            code_data['created_at'] = get_current_timestamp()
            code_data['used_count'] = 0
            code_data['last_used'] = 0
            code_data['redeemed_by'] = []
            
            result = await collection.insert_one(code_data)
            
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"Error adding gift code: {e}")
            return False
    
    async def redeem_gift_code(self, code: str, user_id: int) -> Dict[str, Any]:
        """Redeem gift code for user (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            # Find gift code (case-insensitive)
            gift_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            if not gift_code:
                return {
                    'success': False, 
                    'message': 'Invalid gift code.'
                }
            
            # Check redemption eligibility
            eligibility = GiftCodeModel.check_code_redemption_eligibility(gift_code, user_id)
            
            if not eligibility['eligible']:
                return {
                    'success': False, 
                    'message': eligibility['message']
                }
            
            # Update gift code usage
            redeemed_by = gift_code.get('redeemed_by', [])
            redeemed_by.append(user_id)
            
            update_result = await collection.update_one(
                {"code": {"$regex": f"^{code}$", "$options": "i"}},
                {
                    "$inc": {"used_count": 1},
                    "$set": {
                        "last_used": get_current_timestamp(),
                        "redeemed_by": redeemed_by
                    }
                }
            )
            
            if update_result.modified_count == 0:
                return {
                    'success': False, 
                    'message': 'Error processing gift code redemption.'
                }
            
            # Update user balance
            from services.user_service import UserService
            user_service = UserService()
            
            amount = float(gift_code['amount'])
            balance_updated = await user_service.update_user_balance(user_id, amount, 'add')
            
            if not balance_updated:
                # Rollback gift code update
                await collection.update_one(
                    {"code": {"$regex": f"^{code}$", "$options": "i"}},
                    {
                        "$inc": {"used_count": -1},
                        "$set": {
                            "redeemed_by": gift_code.get('redeemed_by', [])
                        }
                    }
                )
                
                return {
                    'success': False, 
                    'message': 'Error updating user balance.'
                }
            
            return {
                'success': True, 
                'amount': amount
            }
            
        except Exception as e:
            logger.error(f"Error redeeming gift code: {e}")
            return {
                'success': False, 
                'message': 'Error processing gift code redemption.'
            }
    
    async def check_code_exists(self, code: str) -> bool:
        """Check if gift code already exists (case-insensitive)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            existing_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            return existing_code is not None
            
        except Exception as e:
            logger.error(f"Error checking code existence: {e}")
            return False
    
    async def get_gift_code_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """Get gift code by code string"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            gift_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            if gift_code and '_id' in gift_code:
                del gift_code['_id']
            
            return gift_code
            
        except Exception as e:
            logger.error(f"Error getting gift code by code: {e}")
            return None
    
    async def get_gift_codes_by_admin(self, admin_id: int) -> List[Dict[str, Any]]:
        """Get gift codes created by specific admin"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            cursor = collection.find({"created_by": admin_id})
            gift_codes = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for code in gift_codes:
                if '_id' in code:
                    del code['_id']
            
            return gift_codes
            
        except Exception as e:
            logger.error(f"Error getting gift codes by admin: {e}")
            return []
    
    async def get_active_gift_codes(self) -> List[Dict[str, Any]]:
        """Get only active (non-expired, non-exhausted) gift codes"""
        try:
            all_codes = await self.get_all_gift_codes()
            active_codes = []
            current_time = get_current_timestamp()
            
            for code in all_codes:
                # Check if expired
                if (code.get('expiry_date', 0) > 0 and 
                    current_time > code['expiry_date']):
                    continue
                
                # Check if exhausted
                usage_limit = code.get('usage_limit', 0)
                used_count = code.get('used_count', 0)
                if usage_limit > 0 and used_count >= usage_limit:
                    continue
                
                active_codes.append(code)
            
            return active_codes
            
        except Exception as e:
            logger.error(f"Error getting active gift codes: {e}")
            return []
    
    async def delete_gift_code(self, code: str) -> bool:
        """Delete gift code (admin function)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            result = await collection.delete_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting gift code: {e}")
            return False
    
    async def update_gift_code_expiry(self, code: str, expiry_timestamp: int) -> bool:
        """Update gift code expiry date"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            result = await collection.update_one(
                {"code": {"$regex": f"^{code}$", "$options": "i"}},
                {"$set": {"expiry_date": expiry_timestamp}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating gift code expiry: {e}")
            return False
    
    async def get_gift_code_statistics(self) -> Dict[str, Any]:
        """Get comprehensive gift code statistics"""
        try:
            all_codes = await self.get_all_gift_codes()
            return GiftCodeModel.get_gift_code_statistics(all_codes)
            
        except Exception as e:
            logger.error(f"Error getting gift code statistics: {e}")
            return {
                'total_codes': 0,
                'active_codes': 0,
                'expired_codes': 0,
                'exhausted_codes': 0,
                'total_amount': 0,
                'total_redeemed': 0,
                'total_redemptions': 0
            }
    
    async def generate_admin_gift_codes_message(self) -> str:
        """Generate admin gift codes management message"""
        try:
            gift_codes = await self.get_all_gift_codes()
            return GiftCodeModel.format_admin_gift_codes_list_message(gift_codes)
            
        except Exception as e:
            logger.error(f"Error generating admin gift codes message: {e}")
            return "🎫 <b>Gift Codes Management</b>\n\n❌ Error loading gift codes. Please try again later."
    
    async def generate_admin_gift_codes_keyboard(self):
        """Generate admin gift codes management keyboard"""
        try:
            return GiftCodeModel.create_admin_gift_codes_keyboard()
            
        except Exception as e:
            logger.error(f"Error generating admin gift codes keyboard: {e}")
            # Return basic keyboard if there's an error
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])
