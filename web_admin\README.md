# 🌐 Web Admin Panel - Telegram Referral Bot

A comprehensive single-page web administration panel for managing your Telegram referral bot with advanced analytics, user management, and financial oversight.

## 🚀 Features

### 🔐 **Secure Authentication**
- Fixed login code authentication (Code: `6283`)
- Session-based security with 1-hour timeout
- Automatic logout and session management
- Security headers and HTTPS enforcement

### 👥 **User Management**
- Complete user database with detailed profiles
- Advanced search and filtering capabilities
- User status management (ban/unban)
- **Enhanced referral tracking and visualization**
- **Referral chain hierarchy display**
- **Referral earnings and commission tracking**
- Account information display
- Pagination for large datasets
- **Referral-specific filtering options**

### 💰 **Financial Overview**
- Total withdrawals and pending amounts
- User balance summaries
- Commission tracking
- Financial breakdown charts
- Revenue analytics
- Export functionality for financial data

### 🏆 **Leaderboards**
- Top 10 users by referral count
- Top 10 users by total withdrawals
- Top 10 users by current balance
- Real-time ranking system
- Performance metrics

### 📊 **Bot Statistics Dashboard**
- Total registered users
- Active users (7-day and 30-day)
- New user registrations
- Conversion rate analytics
- User activity distribution
- Growth metrics with interactive charts

### 🔗 **Referral Chain Management**
- **Visual referral hierarchy display**
- **Complete upline and downline tracking**
- **Commission earnings per referral**
- **Referral date and status tracking**
- **Interactive referral chain visualization**
- **Multi-level referral relationship mapping**

### 📈 **Data Visualization**
- Interactive Chart.js charts
- User growth trends
- Activity distribution
- Financial breakdowns
- **Referral chain visualizations**
- Real-time data updates

### 🔧 **Technical Features**
- Responsive Bootstrap 5 design
- Single-page application architecture
- Real-time data synchronization
- CSV/Excel export capabilities
- Mobile-friendly interface
- Auto-refresh functionality

## 📁 File Structure

```
web_admin/
├── index.php              # Main admin panel (single-page app)
├── config.php             # Configuration and authentication
├── login.php              # Login page template
├── user_details.php       # User details modal content
├── .htaccess              # Security configuration
└── README.md              # This documentation
```

## 🛠️ Installation

### 1. **Upload Files**
Upload the entire `web_admin/` directory to your server:
```
your-domain.com/referearnbot/web_admin/
```

### 2. **Set Permissions**
Ensure proper file permissions:
```bash
chmod 644 *.php
chmod 644 .htaccess
chmod 755 web_admin/
```

### 3. **Configure Access**
The admin panel uses the existing bot configuration from `../config.php`. No additional setup required.

## 🔑 Access

### **Login Credentials**
- **URL**: `https://your-domain.com/referearnbot/web_admin/`
- **Access Code**: `1412`

### **Security Features**
- Session timeout: 1 hour
- Automatic logout on inactivity
- Secure headers enabled
- HTTPS enforcement (if available)

## 📱 Usage Guide

### **Dashboard Overview**
1. **Login** with access code `1412`
2. **Overview Tab**: Quick statistics and charts
3. **User Management**: Search, filter, and manage users
4. **Financial**: Revenue and withdrawal analytics
5. **Leaderboards**: Top performers across categories
6. **Statistics**: Detailed bot performance metrics

### **User Management**
- **Search**: By name, username, or user ID
- **Filter**: Active, banned, high balance, pending withdrawals
- **Actions**: View details, edit user, ban/unban
- **Export**: Download user data as CSV

### **Financial Tracking**
- Monitor total withdrawals and pending amounts
- Track user balances and commission earned
- Export financial data for accounting
- View revenue trends and breakdowns

### **Analytics**
- Real-time user activity monitoring
- Conversion rate tracking
- Growth metrics and trends
- Interactive charts and visualizations

## 🔧 Customization

### **Login Code**
To change the login code, edit `web_admin/config.php`:
```php
define('WEB_ADMIN_LOGIN_CODE', 'your-new-code');
```

### **Session Timeout**
To modify session timeout, edit `web_admin/config.php`:
```php
define('WEB_ADMIN_SESSION_TIMEOUT', 7200); // 2 hours
```

### **Styling**
The panel uses Bootstrap 5 with custom CSS. Modify the `<style>` section in `index.php` to customize appearance.

### **Charts**
Charts are powered by Chart.js. Modify the JavaScript section in `index.php` to customize chart types and data.

## 🛡️ Security

### **Built-in Protection**
- Session-based authentication
- CSRF protection via session validation
- Input sanitization and validation
- SQL injection prevention
- XSS protection headers
- File access restrictions

### **Recommended Security**
- Use HTTPS in production
- Regularly update access codes
- Monitor access logs
- Implement IP restrictions if needed
- Keep PHP and server updated

## 📊 Data Sources

The admin panel integrates with your existing bot data:
- **JSON Mode**: Reads from `data/` directory files
- **MySQL Mode**: Connects to bot database
- **Real-time**: Data syncs with bot operations
- **Compatibility**: Works with both storage modes

## 🔄 Auto-Refresh

The panel automatically refreshes data every 5 minutes to ensure real-time accuracy. Manual refresh is also available.

## 📱 Mobile Support

Fully responsive design optimized for:
- Desktop computers
- Tablets
- Mobile phones
- Various screen sizes

## 🆘 Troubleshooting

### **Login Issues**
- Verify access code is correct (`1412`)
- Check session timeout settings
- Clear browser cache and cookies

### **Data Not Loading**
- Verify bot configuration files exist
- Check file permissions
- Ensure storage mode is properly configured

### **Charts Not Displaying**
- Check internet connection (CDN dependencies)
- Verify JavaScript is enabled
- Check browser console for errors

## 🔮 Future Enhancements

Planned features for future versions:
- Multi-admin support with role-based access
- Advanced user communication tools
- Automated report generation
- API integration for external tools
- Enhanced security features
- Custom dashboard widgets

## 📞 Support

For technical support or feature requests:
1. Check existing bot documentation
2. Verify configuration settings
3. Review error logs
4. Test with different browsers

---

**Powered by PHP • Bootstrap 5 • Chart.js**  
**Compatible with Hostinger Shared Hosting**
