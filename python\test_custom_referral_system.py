"""
Test script for the Custom Referral System
Verifies all custom referral functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    BOT_USERNAME = "testbot"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [123456789]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date
})
sys.modules['utils.helpers'] = helpers_module

# Mock database
class MockCollection:
    def __init__(self):
        self.data = []
    
    async def find_one(self, query):
        # Simulate finding custom referral
        if query.get('custom_param') == 'test-param':
            return {
                'custom_param': 'test-param',
                'user_id': 12345,
                'user_name': 'Test User',
                'username': 'testuser',
                'created_by': 999,
                'created_at': mock_get_current_timestamp(),
                'clicks': 5,
                'referrals': 2,
                'active': True
            }
        return None
    
    async def update_one(self, query, update):
        class MockResult:
            modified_count = 1
        return MockResult()
    
    async def insert_one(self, data):
        class MockResult:
            inserted_id = "mock_id"
        return MockResult()
    
    def find(self, query=None):
        class MockCursor:
            async def to_list(self, length=None):
                return [
                    {
                        'custom_param': 'test-param',
                        'user_id': 12345,
                        'user_name': 'Test User',
                        'username': 'testuser',
                        'created_by': 999,
                        'created_at': mock_get_current_timestamp(),
                        'clicks': 5,
                        'referrals': 2,
                        'active': True
                    },
                    {
                        'custom_param': 'premium-offer',
                        'user_id': 67890,
                        'user_name': 'Premium User',
                        'username': 'premiumuser',
                        'created_by': 999,
                        'created_at': mock_get_current_timestamp(),
                        'clicks': 10,
                        'referrals': 3,
                        'active': True
                    }
                ]
        return MockCursor()
    
    async def delete_one(self, query):
        class MockResult:
            deleted_count = 1
        return MockResult()

async def mock_get_collection(name):
    return MockCollection()

# Mock database module
database_module = type('MockDatabase', (), {
    'get_collection': mock_get_collection,
    'COLLECTIONS': {
        'custom_referrals': 'custom_referrals'
    }
})
sys.modules['config.database'] = database_module

# Mock user service
class MockUserService:
    async def get_user(self, user_id):
        return {
            'user_id': user_id,
            'first_name': 'Test User',
            'username': 'testuser',
            'balance': 100.0,
            'banned': False,
            'created_at': mock_get_current_timestamp()
        }

sys.modules['services.user_service'] = type('MockModule', (), {'UserService': MockUserService})

from services.custom_referral_service import CustomReferralService
from models.custom_referral import CustomReferralModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CustomReferralSystemTester:
    """Test the custom referral system"""
    
    def __init__(self):
        self.custom_referral_service = CustomReferralService()
    
    async def run_all_tests(self):
        """Run all custom referral system tests"""
        try:
            logger.info("Starting Custom Referral System Tests...")
            
            # Run tests
            await self.test_custom_referral_validation()
            await self.test_custom_referral_creation()
            await self.test_custom_referral_management()
            await self.test_custom_referral_tracking()
            await self.test_message_formatting()
            await self.test_command_parsing()
            
            logger.info("All Custom Referral System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
    
    async def test_custom_referral_validation(self):
        """Test custom referral parameter validation"""
        logger.info("Testing custom referral validation...")
        
        # Test valid parameter
        validation = self.custom_referral_service.validate_custom_param("valid-param123")
        assert validation['valid'], "Valid parameter should pass validation"
        
        # Test too short parameter
        validation = self.custom_referral_service.validate_custom_param("ab")
        assert not validation['valid'], "Too short parameter should fail validation"
        assert "at least 3 characters" in validation['message'], "Should mention length requirement"
        
        # Test too long parameter
        validation = self.custom_referral_service.validate_custom_param("a" * 51)
        assert not validation['valid'], "Too long parameter should fail validation"
        assert "no more than 50 characters" in validation['message'], "Should mention length limit"
        
        # Test invalid characters
        validation = self.custom_referral_service.validate_custom_param("invalid@param")
        assert not validation['valid'], "Parameter with invalid characters should fail"
        assert "letters, numbers, hyphens, and underscores" in validation['message'], "Should mention allowed characters"
        
        # Test reserved word
        validation = self.custom_referral_service.validate_custom_param("start")
        assert not validation['valid'], "Reserved word should fail validation"
        assert "reserved word" in validation['message'], "Should mention reserved word"
        
        logger.info("✅ Custom referral validation test passed")
    
    async def test_custom_referral_creation(self):
        """Test custom referral creation"""
        logger.info("Testing custom referral creation...")
        
        # Test valid creation
        result = await self.custom_referral_service.create_custom_referral("new-param", 12345, 999)
        assert result['success'], f"Custom referral creation should succeed: {result.get('message', '')}"
        assert result['custom_param'] == "new-param", "Should return correct parameter"
        assert "https://t.me/testbot?start=new-param" in result['custom_link'], "Should generate correct link"
        
        # Test duplicate parameter (should fail)
        result = await self.custom_referral_service.create_custom_referral("test-param", 12345, 999)
        assert not result['success'], "Duplicate parameter should fail"
        assert "already exists" in result['message'], "Should mention parameter exists"
        
        logger.info("✅ Custom referral creation test passed")
    
    async def test_custom_referral_management(self):
        """Test custom referral management operations"""
        logger.info("Testing custom referral management...")
        
        # Test get all referrals
        referrals = await self.custom_referral_service.get_all_custom_referrals()
        assert len(referrals) > 0, "Should return referrals"
        
        # Test get by parameter
        referral = await self.custom_referral_service.get_custom_referral_by_param("test-param")
        assert referral is not None, "Should find existing referral"
        assert referral['custom_param'] == "test-param", "Should return correct referral"
        
        # Test get by user
        user_referrals = await self.custom_referral_service.get_custom_referrals_by_user(12345)
        assert len(user_referrals) >= 0, "Should return user referrals"
        
        # Test update referral
        result = await self.custom_referral_service.update_custom_referral("test-param", "updated-param", 999)
        assert result['success'], "Update should succeed"
        assert result['old_param'] == "test-param", "Should return old parameter"
        assert result['new_param'] == "updated-param", "Should return new parameter"
        
        # Test delete referral
        result = await self.custom_referral_service.delete_custom_referral("test-param", 999)
        assert result['success'], "Delete should succeed"
        assert result['param'] == "test-param", "Should return deleted parameter"
        
        logger.info("✅ Custom referral management test passed")
    
    async def test_custom_referral_tracking(self):
        """Test custom referral tracking"""
        logger.info("Testing custom referral tracking...")
        
        # Test click tracking
        result = await self.custom_referral_service.track_custom_referral_click("test-param")
        assert result, "Click tracking should succeed"
        
        # Test conversion tracking
        result = await self.custom_referral_service.track_custom_referral_conversion("test-param")
        assert result, "Conversion tracking should succeed"
        
        logger.info("✅ Custom referral tracking test passed")
    
    async def test_message_formatting(self):
        """Test message formatting"""
        logger.info("Testing message formatting...")
        
        # Test referral list message
        referrals = await self.custom_referral_service.get_all_custom_referrals()
        message = CustomReferralModel.format_custom_referral_list_message(referrals)
        assert "Custom Referral Links" in message, "Should contain title"
        assert "Total Links:" in message, "Should contain count"
        
        # Test empty list message
        empty_message = CustomReferralModel.format_custom_referral_list_message([])
        assert "No custom referral links found" in empty_message, "Should handle empty list"
        
        # Test help message
        help_message = CustomReferralModel.format_custom_referral_help_message()
        assert "Custom Referral Link Management" in help_message, "Should contain title"
        assert "Available Commands:" in help_message, "Should contain commands section"
        
        # Test success messages
        success_message = CustomReferralModel.format_custom_referral_creation_success_message(
            "test-param", "https://t.me/testbot?start=test-param", "Test User", 12345
        )
        assert "Custom Referral Link Created" in success_message, "Should contain success title"
        assert "test-param" in success_message, "Should contain parameter"
        
        logger.info("✅ Message formatting test passed")
    
    async def test_command_parsing(self):
        """Test command parsing"""
        logger.info("Testing command parsing...")
        
        # Test list command
        parsed = CustomReferralModel.parse_custom_referral_command("/customref list")
        assert parsed['valid'], "List command should be valid"
        assert parsed['action'] == 'list', "Should parse list action"
        
        # Test create command
        parsed = CustomReferralModel.parse_custom_referral_command("/customref create test-param 12345")
        assert parsed['valid'], "Create command should be valid"
        assert parsed['action'] == 'create', "Should parse create action"
        assert parsed['param'] == 'test-param', "Should parse parameter"
        assert parsed['user_id'] == 12345, "Should parse user ID"
        
        # Test edit command
        parsed = CustomReferralModel.parse_custom_referral_command("/customref edit old-param new-param")
        assert parsed['valid'], "Edit command should be valid"
        assert parsed['action'] == 'edit', "Should parse edit action"
        assert parsed['old_param'] == 'old-param', "Should parse old parameter"
        assert parsed['new_param'] == 'new-param', "Should parse new parameter"
        
        # Test delete command
        parsed = CustomReferralModel.parse_custom_referral_command("/customref delete test-param")
        assert parsed['valid'], "Delete command should be valid"
        assert parsed['action'] == 'delete', "Should parse delete action"
        assert parsed['param'] == 'test-param', "Should parse parameter"
        
        # Test invalid command
        parsed = CustomReferralModel.parse_custom_referral_command("/customref invalid")
        assert not parsed['valid'], "Invalid command should fail"
        assert "Unknown action" in parsed['message'], "Should mention unknown action"
        
        logger.info("✅ Command parsing test passed")

def main():
    """Main test function"""
    async def run_tests():
        tester = CustomReferralSystemTester()
        
        try:
            success = await tester.run_all_tests()
            
            if success:
                print("\n🎉 All Custom Referral System tests PASSED!")
                print("The custom referral system is ready for production use.")
            else:
                print("\n❌ Some tests FAILED!")
                print("Please check the logs for details.")
                sys.exit(1)
                
        except Exception as e:
            print(f"\n💥 Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(run_tests())

if __name__ == "__main__":
    main()
