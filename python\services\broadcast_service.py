"""
Broadcast service for sending messages to all users
Maintains identical functionality to PHP version
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class BroadcastService:
    """Service for broadcasting messages to users"""
    
    def __init__(self):
        self.bot = None
    
    def set_bot(self, bot: Bot):
        """Set the bot instance for broadcasting"""
        self.bot = bot
    
    async def broadcast_text_message(self, message: str, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast text message to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            
            logger.info(f"Starting broadcast to {total_users} users")
            
            # Send messages in batches to avoid rate limiting
            batch_size = 30  # Telegram rate limit
            delay_between_batches = 1  # 1 second delay
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Send to batch
                for user in batch:
                    try:
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=message,
                            parse_mode='HTML'
                        )
                        successful_sends += 1
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send broadcast to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending broadcast to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'text', message, total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'message': f'Broadcast completed. Sent to {successful_sends}/{total_users} users.'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_text_message: {e}")
            return {
                'success': False,
                'message': f'Broadcast failed: {str(e)}'
            }
    
    async def broadcast_gift_message(self, gift_amount: float, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast gift message and add balance to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1, 'first_name': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            total_gift_amount = 0
            
            logger.info(f"Starting gift broadcast to {total_users} users")
            
            # Prepare gift message
            gift_message = f"🎁 <b>Gift Alert!</b>\n\n"
            gift_message += f"You have received ₹{gift_amount} as a gift from admin!\n\n"
            gift_message += f"💰 <b>Gift Amount:</b> ₹{gift_amount}\n"
            gift_message += f"🎉 <b>Enjoy your gift!</b>"
            
            # Send messages and add balance in batches
            batch_size = 30
            delay_between_batches = 1
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Process batch
                for user in batch:
                    try:
                        # Add balance to user
                        await users_collection.update_one(
                            {'user_id': user['user_id']},
                            {'$inc': {'balance': gift_amount}}
                        )
                        
                        # Send gift message
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=gift_message,
                            parse_mode='HTML'
                        )
                        
                        successful_sends += 1
                        total_gift_amount += gift_amount
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send gift to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending gift to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'gift', f"Gift: ₹{gift_amount}", total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'total_gift_amount': total_gift_amount,
                'message': f'Gift broadcast completed. Sent ₹{gift_amount} to {successful_sends}/{total_users} users. Total distributed: ₹{total_gift_amount}'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_gift_message: {e}")
            return {
                'success': False,
                'message': f'Gift broadcast failed: {str(e)}'
            }
    
    async def _log_broadcast(self, admin_id: int, broadcast_type: str, content: str, total_users: int, successful: int, failed: int):
        """Log broadcast activity"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            log_entry = {
                'admin_id': admin_id,
                'action': f'broadcast_{broadcast_type}',
                'details': {
                    'type': broadcast_type,
                    'content': content[:100],  # Truncate long content
                    'total_users': total_users,
                    'successful_sends': successful,
                    'failed_sends': failed
                },
                'timestamp': get_current_timestamp()
            }
            
            await logs_collection.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging broadcast: {e}")
    
    async def get_broadcast_statistics(self) -> Dict[str, Any]:
        """Get broadcast statistics"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            # Get recent broadcasts
            cursor = logs_collection.find(
                {'action': {'$in': ['broadcast_text', 'broadcast_gift']}},
                sort=[('timestamp', -1)],
                limit=10
            )
            recent_broadcasts = await cursor.to_list(length=None)
            
            # Get total broadcast count
            total_broadcasts = await logs_collection.count_documents({
                'action': {'$in': ['broadcast_text', 'broadcast_gift']}
            })
            
            return {
                'success': True,
                'total_broadcasts': total_broadcasts,
                'recent_broadcasts': recent_broadcasts
            }
            
        except Exception as e:
            logger.error(f"Error getting broadcast statistics: {e}")
            return {
                'success': False,
                'message': f'Error getting statistics: {str(e)}'
            }
