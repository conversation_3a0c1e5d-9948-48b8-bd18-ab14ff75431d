<?php
/**
 * Referral Chain Visualization
 * Displays comprehensive referral hierarchy for a user
 */

require_once 'config.php';
requireAuth();

$userId = $_GET['user_id'] ?? null;

if (!$userId) {
    echo '<div class="alert alert-danger">User ID not provided</div>';
    exit;
}

// Get referral chain data
$chain = getReferralChain($userId);

if (!$chain['user']) {
    echo '<div class="alert alert-warning">User not found</div>';
    exit;
}

$user = $chain['user'];
?>

<style>
.referral-node {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 15px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.referral-node.current-user {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.referral-node.upline {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-color: #28a745;
}

.referral-node.downline {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
    border-color: #17a2b8;
}

.referral-node:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.chain-connector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}

.chain-line {
    width: 2px;
    height: 30px;
    background: #667eea;
    position: relative;
}

.chain-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: #667eea;
    border-radius: 50%;
}

.referral-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
}

.earnings-badge {
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.date-badge {
    background: #6c757d;
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 11px;
}

.referral-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
</style>

<div class="container-fluid">
    <!-- Current User (Center) -->
    <div class="text-center mb-4">
        <h4><i class="fas fa-sitemap me-2"></i>Referral Chain for <?php echo htmlspecialchars($user['first_name']); ?></h4>
        <p class="text-muted">Complete referral hierarchy visualization</p>
    </div>
    
    <!-- Upline Section -->
    <?php if (!empty($chain['upline'])): ?>
    <div class="mb-4">
        <h5 class="text-center mb-3"><i class="fas fa-arrow-up me-2"></i>Upline (Who Referred This User)</h5>
        <div class="d-flex flex-column align-items-center">
            <?php foreach (array_reverse($chain['upline']) as $index => $uplineUser): ?>
                <div class="referral-node upline">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="avatar-circle me-3" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div>
                            <strong><?php echo htmlspecialchars($uplineUser['first_name']); ?></strong>
                            <br>
                            <small>@<?php echo htmlspecialchars($uplineUser['username'] ?: 'N/A'); ?></small>
                            <br>
                            <small>ID: <?php echo $uplineUser['user_id']; ?></small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="badge bg-light text-dark">Level <?php echo count($chain['upline']) - $index; ?> Up</span>
                        <br>
                        <small>Balance: ₹<?php echo number_format($uplineUser['balance'] ?? 0, 2); ?></small>
                        <br>
                        <small>Referrals: <?php echo $uplineUser['total_referrals'] ?? 0; ?></small>
                    </div>
                </div>
                <?php if ($index < count($chain['upline']) - 1): ?>
                <div class="chain-connector">
                    <div class="chain-line"></div>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
            <div class="chain-connector">
                <div class="chain-line"></div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Current User -->
    <div class="d-flex justify-content-center mb-4">
        <div class="referral-node current-user" style="min-width: 300px;">
            <div class="d-flex align-items-center justify-content-center mb-3">
                <div class="avatar-circle me-3" style="background: rgba(255,255,255,0.2); width: 50px; height: 50px; font-size: 20px;">
                    <i class="fas fa-user-crown"></i>
                </div>
                <div>
                    <h5 class="mb-1"><?php echo htmlspecialchars($user['first_name']); ?></h5>
                    <p class="mb-1">@<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?></p>
                    <small>ID: <?php echo $user['user_id']; ?></small>
                </div>
            </div>
            <div class="referral-stats" style="background: rgba(255,255,255,0.1);">
                <div class="row text-center">
                    <div class="col-4">
                        <strong>₹<?php echo number_format($user['balance'] ?? 0, 2); ?></strong>
                        <br>
                        <small>Balance</small>
                    </div>
                    <div class="col-4">
                        <strong><?php echo $user['total_referrals'] ?? 0; ?></strong>
                        <br>
                        <small>Referrals</small>
                    </div>
                    <div class="col-4">
                        <strong>₹<?php echo number_format($user['referral_earnings'] ?? 0, 2); ?></strong>
                        <br>
                        <small>Earned</small>
                    </div>
                </div>
            </div>
            <div class="mt-2">
                <span class="badge bg-warning text-dark">
                    <i class="fas fa-star me-1"></i>Current User
                </span>
            </div>
        </div>
    </div>
    
    <!-- Downline Section -->
    <?php if (!empty($chain['downline'])): ?>
    <div class="mb-4">
        <div class="chain-connector">
            <div class="chain-line"></div>
        </div>
        <h5 class="text-center mb-3"><i class="fas fa-arrow-down me-2"></i>Downline (Users Referred by This User)</h5>
        <div class="referral-grid">
            <?php foreach ($chain['downline'] as $downlineUser): ?>
            <div class="referral-node downline">
                <div class="d-flex align-items-center mb-2">
                    <div class="avatar-circle me-3" style="background: rgba(255,255,255,0.2);">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="flex-grow-1">
                        <strong><?php echo htmlspecialchars($downlineUser['first_name'] ?? $downlineUser['referred_user_name'] ?? 'Unknown'); ?></strong>
                        <br>
                        <small>@<?php echo htmlspecialchars($downlineUser['username'] ?? 'N/A'); ?></small>
                        <br>
                        <small>ID: <?php echo $downlineUser['user_id'] ?? $downlineUser['referred_user_id'] ?? 'N/A'; ?></small>
                    </div>
                </div>
                
                <div class="referral-stats">
                    <div class="row text-center">
                        <div class="col-6">
                            <strong>₹<?php echo number_format($downlineUser['balance'] ?? 0, 2); ?></strong>
                            <br>
                            <small>Balance</small>
                        </div>
                        <div class="col-6">
                            <strong><?php echo $downlineUser['total_referrals'] ?? 0; ?></strong>
                            <br>
                            <small>Referrals</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-2">
                    <?php if (isset($downlineUser['commission_earned']) && $downlineUser['commission_earned'] > 0): ?>
                        <span class="earnings-badge">
                            <i class="fas fa-coins me-1"></i>₹<?php echo number_format($downlineUser['commission_earned'], 2); ?> earned
                        </span>
                        <br>
                    <?php endif; ?>
                    
                    <?php if (isset($downlineUser['referral_date']) && !empty($downlineUser['referral_date'])): ?>
                        <span class="date-badge mt-1">
                            <i class="fas fa-calendar me-1"></i><?php echo date('M d, Y', strtotime($downlineUser['referral_date'])); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-light" onclick="viewUserDetails(<?php echo $downlineUser['user_id'] ?? $downlineUser['referred_user_id']; ?>)">
                        <i class="fas fa-eye me-1"></i>View Details
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php else: ?>
    <div class="text-center">
        <div class="chain-connector">
            <div class="chain-line"></div>
        </div>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            This user hasn't referred anyone yet.
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Summary Statistics -->
    <div class="card mt-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Referral Chain Summary</h6>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <h4 class="text-primary"><?php echo count($chain['upline']); ?></h4>
                    <p class="mb-0">Levels Up</p>
                </div>
                <div class="col-md-3">
                    <h4 class="text-success"><?php echo count($chain['downline']); ?></h4>
                    <p class="mb-0">Direct Referrals</p>
                </div>
                <div class="col-md-3">
                    <h4 class="text-info">₹<?php echo number_format($user['referral_earnings'] ?? 0, 2); ?></h4>
                    <p class="mb-0">Total Earnings</p>
                </div>
                <div class="col-md-3">
                    <h4 class="text-warning"><?php echo $user['total_referrals'] ?? 0; ?></h4>
                    <p class="mb-0">Total Referrals</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Function to view user details (will use parent window's function)
function viewUserDetails(userId) {
    if (window.parent && window.parent.viewUserDetails) {
        window.parent.viewUserDetails(userId);
    } else {
        alert('User details functionality not available');
    }
}
</script>
