"""
Test script for the Task Management System
Verifies all task-related functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from config.database import db_manager
from services.task_service import TaskService
from services.user_service import UserService
from services.referral_service import ReferralService
from models.task import TaskModel, TaskSubmissionModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskSystemTester:
    """Test the task management system"""
    
    def __init__(self):
        self.task_service = TaskService()
        self.user_service = UserService()
        self.referral_service = ReferralService()
    
    async def run_all_tests(self):
        """Run all task system tests"""
        try:
            logger.info("Starting Task Management System Tests...")
            
            # Connect to database
            if not await db_manager.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Run tests
            await self.test_task_creation()
            await self.test_task_retrieval()
            await self.test_task_submission()
            await self.test_task_approval()
            await self.test_task_rejection()
            await self.test_task_statistics()
            
            logger.info("All Task Management System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
        finally:
            await db_manager.disconnect()
    
    async def test_task_creation(self):
        """Test task creation functionality"""
        logger.info("Testing task creation...")
        
        # Create test task
        task_data = TaskModel.create_new_task(
            name="Test Task",
            description="This is a test task for verification",
            reward_amount=50,
            media_url="",
            status="active",
            created_by=123456789
        )
        
        # Validate task data
        assert TaskModel.validate_task_data(task_data), "Task data validation failed"
        
        # Add task to database
        success = await self.task_service.add_task(task_data)
        assert success, "Failed to add task to database"
        
        logger.info("✅ Task creation test passed")
        return task_data['task_id']
    
    async def test_task_retrieval(self):
        """Test task retrieval functionality"""
        logger.info("Testing task retrieval...")
        
        # Get all tasks
        all_tasks = await self.task_service.get_all_tasks()
        assert len(all_tasks) > 0, "No tasks found"
        
        # Get active tasks
        active_tasks = await self.task_service.get_active_tasks()
        assert len(active_tasks) > 0, "No active tasks found"
        
        # Get specific task
        task_id = all_tasks[0]['task_id']
        task = await self.task_service.get_task_by_id(task_id)
        assert task is not None, "Failed to retrieve specific task"
        assert task['task_id'] == task_id, "Retrieved wrong task"
        
        logger.info("✅ Task retrieval test passed")
        return task_id
    
    async def test_task_submission(self):
        """Test task submission functionality"""
        logger.info("Testing task submission...")
        
        # Get a test task
        active_tasks = await self.task_service.get_active_tasks()
        if not active_tasks:
            logger.warning("No active tasks for submission test")
            return None
        
        task_id = active_tasks[0]['task_id']
        test_user_id = 987654321
        test_file_id = "test_file_id_123"
        
        # Create submission
        submission_data = TaskSubmissionModel.create_new_submission(
            user_id=test_user_id,
            task_id=task_id,
            file_id=test_file_id,
            status="pending"
        )
        
        # Validate submission data
        assert TaskSubmissionModel.validate_submission_data(submission_data), "Submission data validation failed"
        
        # Add submission to database
        submission_id = await self.task_service.add_task_submission(submission_data)
        assert submission_id is not None, "Failed to add task submission"
        
        # Verify submission exists
        submission = await self.task_service.get_submission_by_id(submission_id)
        assert submission is not None, "Failed to retrieve submission"
        assert submission['user_id'] == test_user_id, "Submission user_id mismatch"
        assert submission['task_id'] == task_id, "Submission task_id mismatch"
        
        logger.info("✅ Task submission test passed")
        return submission_id
    
    async def test_task_approval(self):
        """Test task approval functionality"""
        logger.info("Testing task approval...")
        
        # Get pending submissions
        pending_submissions = await self.task_service.get_pending_task_submissions()
        if not pending_submissions:
            logger.warning("No pending submissions for approval test")
            return
        
        submission_id = pending_submissions[0]['submission_id']
        
        # Create test user if not exists
        test_user_id = pending_submissions[0]['user_id']
        existing_user = await self.user_service.get_user(test_user_id)
        if not existing_user:
            await self.user_service.create_user(
                test_user_id, "Test User", "", "testuser", "None"
            )
        
        # Get user balance before approval
        user = await self.user_service.get_user(test_user_id)
        balance_before = user.get('balance', 0)
        
        # Approve submission
        success = await self.task_service.approve_task_submission(submission_id, "Test approval")
        assert success, "Failed to approve task submission"
        
        # Verify submission status updated
        submission = await self.task_service.get_submission_by_id(submission_id)
        assert submission['status'] == 'approved', "Submission status not updated"
        
        # Verify user balance increased
        user = await self.user_service.get_user(test_user_id)
        balance_after = user.get('balance', 0)
        assert balance_after > balance_before, "User balance not increased"
        
        logger.info("✅ Task approval test passed")
    
    async def test_task_rejection(self):
        """Test task rejection functionality"""
        logger.info("Testing task rejection...")
        
        # Create a test submission for rejection
        active_tasks = await self.task_service.get_active_tasks()
        if not active_tasks:
            logger.warning("No active tasks for rejection test")
            return
        
        task_id = active_tasks[0]['task_id']
        test_user_id = 111222333
        test_file_id = "test_file_id_reject"
        
        submission_data = TaskSubmissionModel.create_new_submission(
            user_id=test_user_id,
            task_id=task_id,
            file_id=test_file_id,
            status="pending"
        )
        
        submission_id = await self.task_service.add_task_submission(submission_data)
        assert submission_id is not None, "Failed to create test submission"
        
        # Reject submission
        success = await self.task_service.reject_task_submission(submission_id, "Test rejection")
        assert success, "Failed to reject task submission"
        
        # Verify submission status updated
        submission = await self.task_service.get_submission_by_id(submission_id)
        assert submission['status'] == 'rejected', "Submission status not updated to rejected"
        assert submission['admin_note'] == 'Test rejection', "Admin note not saved"
        
        logger.info("✅ Task rejection test passed")
    
    async def test_task_statistics(self):
        """Test task statistics functionality"""
        logger.info("Testing task statistics...")
        
        # Get task statistics
        stats = await self.task_service.get_task_statistics()
        
        # Verify statistics structure
        required_fields = [
            'total_tasks', 'active_tasks', 'inactive_tasks',
            'total_submissions', 'pending_submissions',
            'approved_submissions', 'rejected_submissions',
            'total_rewards_distributed'
        ]
        
        for field in required_fields:
            assert field in stats, f"Missing statistics field: {field}"
            assert isinstance(stats[field], int), f"Statistics field {field} is not an integer"
        
        logger.info("✅ Task statistics test passed")
        logger.info(f"Statistics: {stats}")
    
    async def cleanup_test_data(self):
        """Clean up test data (optional)"""
        logger.info("Cleaning up test data...")
        
        # Note: In a real scenario, you might want to clean up test data
        # For now, we'll leave it for manual inspection
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main test function"""
    tester = TaskSystemTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 All Task Management System tests PASSED!")
            print("The task system is ready for production use.")
        else:
            print("\n❌ Some tests FAILED!")
            print("Please check the logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
