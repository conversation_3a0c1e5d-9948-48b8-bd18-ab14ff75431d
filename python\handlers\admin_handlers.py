"""
Admin command handlers
Maintains identical functionality to PHP version
"""

import logging
from typing import Op<PERSON>, Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from services.admin_service import AdminService
from services.user_service import UserService
from utils.helpers import is_admin, get_rank_emoji, get_current_date
from utils.constants import ADMIN_ACCESS_DENIED

logger = logging.getLogger(__name__)

class AdminHandlers:
    """Handles admin-related commands and interactions"""
    
    def __init__(self):
        self.admin_service = AdminService()
        self.user_service = UserService()
    
    async def handle_admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /admin command and 'admin' callback (matching PHP version)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id

        try:
            if not is_admin(user_id):
                return

            # Get admin menu
            admin_menu = await self._get_admin_menu()
            admin_message = "🔧 <b>Admin Panel</b>\n\nChoose an option:"

            # Check if this is a callback query (navigation) or text command
            if update.callback_query:
                # This is navigation from a "Back to Admin Panel" button
                await update.callback_query.edit_message_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )
            else:
                # This is the /admin text command
                await update.message.reply_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_admin_command: {e}")

            # Handle error response based on update type
            try:
                if update.callback_query:
                    await update.callback_query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
                elif update.message:
                    await update.message.reply_text(
                        "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                        parse_mode='HTML'
                    )
                else:
                    logger.error("Both callback_query and message are None in handle_admin_command")
            except Exception as error_e:
                logger.error(f"Error in error handling for handle_admin_command: {error_e}")
    
    async def handle_rank_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /rank command - Display withdrawal statistics for top referrers"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        
        try:
            if not is_admin(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return
            
            # Get top users by withdrawal amount
            top_users = await self.user_service.get_top_users_by_withdrawals(15)
            
            if not top_users:
                await update.message.reply_text(
                    "📊 <b>Withdrawal Rankings</b>\n\n❌ No withdrawal data available yet.\n\nUsers need to make successful withdrawals to appear in rankings.",
                    parse_mode='HTML'
                )
                return
            
            # Format the ranking message
            message = "🏆 <b>TOP WITHDRAWAL RANKINGS</b>\n"
            message += "📊 <i>Top 15 Users by Total Successful Withdrawals</i>\n\n"
            
            rank = 1
            for user in top_users:
                # Get rank emoji
                rank_emoji = get_rank_emoji(rank)
                
                # Format user display name (handle empty names)
                display_name = user.get('first_name', 'Unknown User')
                if display_name:
                    display_name = display_name[:20]  # Truncate long names
                else:
                    display_name = 'Unknown User'
                
                username = user.get('username', '')
                username_display = f"@{username}" if username else 'No username'
                
                # Format withdrawal count
                withdrawal_count = user.get('withdrawal_count', 0)
                withdrawal_text = 'withdrawal' if withdrawal_count == 1 else 'withdrawals'
                
                # Add banned indicator if user is banned
                status_indicator = ' 🚫' if user.get('banned', False) else ''
                
                # Format withdrawal amount
                withdrawal_amount = user.get('successful_withdraw', 0)
                
                # Add user to ranking
                message += f"{rank_emoji} <b>#{rank}</b> - {display_name}{status_indicator}\n"
                message += f"   👤 {username_display} (ID: {user['user_id']})\n"
                message += f"   💰 ₹{withdrawal_amount:,.2f} ({withdrawal_count} {withdrawal_text})\n"
                message += f"   👥 {user.get('total_referrals', 0)} referrals\n\n"
                
                rank += 1
            
            # Add summary statistics
            total_withdrawals = sum(user.get('successful_withdraw', 0) for user in top_users)
            total_referrals = sum(user.get('total_referrals', 0) for user in top_users)
            total_withdrawal_count = sum(user.get('withdrawal_count', 0) for user in top_users)
            average_withdrawal = round(total_withdrawals / len(top_users), 2) if top_users else 0
            average_referrals = round(total_referrals / len(top_users), 1) if top_users else 0
            
            # Find highest single withdrawal and most referrals
            highest_withdrawal = max((user.get('successful_withdraw', 0) for user in top_users), default=0)
            most_referrals = max((user.get('total_referrals', 0) for user in top_users), default=0)
            
            message += "📊 <b>SUMMARY STATISTICS</b>\n"
            message += f"💰 Total Withdrawals: ₹{total_withdrawals:,.2f}\n"
            message += f"👥 Total Referrals: {total_referrals}\n"
            message += f"🔢 Total Withdrawal Transactions: {total_withdrawal_count}\n"
            message += f"📈 Average Withdrawal: ₹{average_withdrawal}\n"
            message += f"📊 Average Referrals: {average_referrals}\n"
            message += f"🏆 Highest Single User: ₹{highest_withdrawal:,.2f}\n"
            message += f"👑 Most Referrals: {most_referrals}\n\n"
            
            # Add footer information
            message += "📈 <i>Rankings based on total successful withdrawal amounts</i>\n"
            message += "🔄 <i>Data updated in real-time</i>\n"
            message += f"📅 <i>Generated: {get_current_date()}</i>\n"
            message += "💾 <i>Storage: MongoDB</i>"
            
            # Send the ranking message
            await update.message.reply_text(message, parse_mode='HTML')
            
        except Exception as e:
            logger.error(f"Error in handle_rank_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nFailed to retrieve ranking data. Please try again later.",
                parse_mode='HTML'
            )
    
    async def handle_custom_referral_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /customref command"""
        user_id = update.effective_user.id
        
        try:
            if not is_admin(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return
            
            # Parse command arguments
            text = update.message.text
            parts = text.split(' ')
            
            if len(parts) < 2:
                await self._show_custom_referral_help(update)
                return
            
            command = parts[1]
            parameters = parts[2:] if len(parts) > 2 else []
            
            # Route to appropriate handler
            if command == 'list':
                await self._handle_custom_referral_list(update)
            elif command == 'create' and len(parameters) >= 2:
                await self._handle_custom_referral_create(update, parameters[0], parameters[1])
            elif command == 'edit' and len(parameters) >= 2:
                await self._handle_custom_referral_edit(update, parameters[0], parameters[1])
            elif command == 'delete' and len(parameters) >= 1:
                await self._handle_custom_referral_delete(update, parameters[0])
            elif command == 'view' and len(parameters) >= 1:
                await self._handle_custom_referral_view(update, parameters[0])
            else:
                await self._show_custom_referral_help(update)
                
        except Exception as e:
            logger.error(f"Error in handle_custom_referral_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    async def _get_admin_menu(self) -> InlineKeyboardMarkup:
        """Get admin menu keyboard"""
        keyboard = [
            [
                InlineKeyboardButton('➕ Add Balance', callback_data='add'),
                InlineKeyboardButton('➖ Remove Balance', callback_data='remove')
            ],
            [
                InlineKeyboardButton('🚫 Ban User', callback_data='ban'),
                InlineKeyboardButton('✅ Unban User', callback_data='unban')
            ],
            [
                InlineKeyboardButton('📢 Main Channel', callback_data='mainChannel'),
                InlineKeyboardButton('📋 Private Logs', callback_data='privateLogsChannel')
            ],
            [
                InlineKeyboardButton('⚙️ Maintenance', callback_data='maintenanceStatus'),
                InlineKeyboardButton('🔑 OTP API Key', callback_data='OTPWebsiteAPIKey')
            ],
            [
                InlineKeyboardButton('💰 Per Refer Amount', callback_data='perReferAmount'),
                InlineKeyboardButton('🎁 Joining Bonus', callback_data='joiningBonusAmount')
            ],
            [
                InlineKeyboardButton('👤 Check User Record', callback_data='checkUserRecord'),
                InlineKeyboardButton('✅ Pass Withdrawal', callback_data='passUserWithdrawal')
            ],
            [
                InlineKeyboardButton('❌ Fail Withdrawal', callback_data='failUserWithdrawal'),
                InlineKeyboardButton('🎁 Broadcast Gift', callback_data='broadcastGiftButton')
            ],
            [
                InlineKeyboardButton('📢 Broadcast Text', callback_data='broadcastText'),
                InlineKeyboardButton('➕ Add Force Sub', callback_data='addForceSubChannel')
            ],
            [
                InlineKeyboardButton('➖ Remove Force Sub', callback_data='removeForceSubChannel'),
                InlineKeyboardButton('👁️ View Force Subs', callback_data='viewForceSubChannels')
            ],
            [
                InlineKeyboardButton('💸 Manage Withdrawals', callback_data='manageWithdrawals'),
                InlineKeyboardButton('⚙️ Withdrawal Settings', callback_data='withdrawal_settings')
            ],
            [
                InlineKeyboardButton('🎯 Extra Rewards', callback_data='extraRewards'),
                InlineKeyboardButton('🎁 Gift Codes', callback_data='viewAllGiftCodes')
            ],
            [
                InlineKeyboardButton('📝 Manage Tasks', callback_data='manageTasks'),
                InlineKeyboardButton('🏆 Level Rewards', callback_data='configureLevelRewards')
            ],
            [
                InlineKeyboardButton('🔗 Custom Referrals', callback_data='customReferralLinks'),
                InlineKeyboardButton('🔔 Force Subscription', callback_data='viewForceChannels')
            ],
            [
                InlineKeyboardButton('📊 Analytics Dashboard', callback_data='analytics_overview'),
                InlineKeyboardButton('👥 Manage User Accounts', callback_data='manageUserAccounts')
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    # Placeholder methods for admin handlers
    # These will be implemented as we build out the respective services
    
    async def handle_add_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add balance callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "💰 <b>Add Balance to User</b>\n\n"
            message += "Enter the User ID to add balance to:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for add balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_balance_user_id')

        except Exception as e:
            logger.error(f"Error in handle_add_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove balance callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "💰 <b>Remove Balance from User</b>\n\n"
            message += "Enter the User ID to remove balance from:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for remove balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_balance_user_id')

        except Exception as e:
            logger.error(f"Error in handle_remove_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle ban user callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🚫 <b>Ban User</b>\n\n"
            message += "Enter the User ID to ban:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for ban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'ban_user_id')

        except Exception as e:
            logger.error(f"Error in handle_ban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unban user callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✅ <b>Unban User</b>\n\n"
            message += "Enter the User ID to unban:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for unban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'unban_user_id')

        except Exception as e:
            logger.error(f"Error in handle_unban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_set_main_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set main channel callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "📢 <b>Set Main Channel</b>\n\n"
            message += "Enter channel username without '@' to set as main channel.\n\n"
            message += "💡 <b>Example:</b> channelname\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_main_channel')

        except Exception as e:
            logger.error(f"Error in handle_set_main_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_set_private_logs_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set private logs channel callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "📋 <b>Set Private Logs Channel</b>\n\n"
            message += "Enter channel username or ID for private logs.\n\n"
            message += "💡 <b>Examples:</b>\n"
            message += "• @channelname\n"
            message += "• -100*********0\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_private_logs_channel')

        except Exception as e:
            logger.error(f"Error in handle_set_private_logs_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_maintenance_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set maintenance status callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Toggle maintenance mode
            result = await admin_service.toggle_maintenance_mode(user_id)

            if result['success']:
                await query.answer(f"✅ {result['message']}", show_alert=True)
                # Refresh admin panel to show updated status
                admin_menu = await self._get_admin_menu()
                await query.edit_message_text(
                    "🔧 <b>Admin Panel</b>\n\nSelect an option:",
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )
            else:
                await query.answer(f"❌ {result['message']}", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_set_maintenance_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_otp_api_key(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set OTP API key callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🔑 <b>Set OTP API Key</b>\n\n"
            message += "Enter your API Key from this website: renflair.in\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_otp_api_key')

        except Exception as e:
            logger.error(f"Error in handle_set_otp_api_key: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_per_refer_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set per refer amount callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "💰 <b>Set Per Refer Amount</b>\n\n"
            message += "Enter per refer amount range in format: min-max\n\n"
            message += "💡 <b>Example:</b> 20-50\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_per_refer_amount')

        except Exception as e:
            logger.error(f"Error in handle_set_per_refer_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_joining_bonus_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set joining bonus amount callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🎁 <b>Set Joining Bonus Amount</b>\n\n"
            message += "Enter joining bonus amount range in format: min-max\n\n"
            message += "💡 <b>Example:</b> 20-50\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_joining_bonus_amount')

        except Exception as e:
            logger.error(f"Error in handle_set_joining_bonus_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_check_user_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle check user record callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "👤 <b>Check User Record</b>\n\n"
            message += "Enter the User ID to check:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for check user record
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'check_user_record')

        except Exception as e:
            logger.error(f"Error in handle_check_user_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_pass_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle pass user withdrawal callback"""
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def handle_fail_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle fail user withdrawal callback"""
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def handle_broadcast_gift_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast gift button callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Check if admin has active broadcast
            from services.admin_service import AdminService
            admin_service = AdminService()

            active_broadcast = await admin_service.get_active_broadcast(user_id)
            if active_broadcast:
                message = f"⚠️ <b>Active Broadcast Found</b>\n\n"
                message += f"🆔 <b>Broadcast ID:</b> {active_broadcast['broadcast_id'][-8:]}\n"
                message += f"📊 <b>Status:</b> {active_broadcast['status'].title()}\n"
                message += f"👥 <b>Target Users:</b> {active_broadcast['total_users']}\n"
                message += f"✅ <b>Sent:</b> {active_broadcast.get('sent_count', 0)}\n"
                message += f"❌ <b>Failed:</b> {active_broadcast.get('failed_count', 0)}\n\n"
                message += "Please wait for the current broadcast to complete or send /cancel to stop it."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = "🎁 <b>Broadcast Gift</b>\n\n"
            message += "Let's create a gift broadcast for users to join a channel and claim rewards.\n\n"
            message += "📝 <b>Step 1:</b> Enter the channel username or invite link\n\n"
            message += "💡 <b>Examples:</b>\n"
            message += "• @channelname\n"
            message += "• https://t.me/channelname\n"
            message += "• https://t.me/+invitelink (for private channels)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for gift broadcast
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'gift_broadcast_channel')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_gift_button: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast text callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Check if admin has active broadcast
            from services.admin_service import AdminService
            admin_service = AdminService()

            active_broadcast = await admin_service.get_active_broadcast(user_id)
            if active_broadcast:
                message = f"⚠️ <b>Active Broadcast Found</b>\n\n"
                message += f"🆔 <b>Broadcast ID:</b> {active_broadcast['broadcast_id'][-8:]}\n"
                message += f"📊 <b>Status:</b> {active_broadcast['status'].title()}\n"
                message += f"👥 <b>Target Users:</b> {active_broadcast['total_users']}\n"
                message += f"✅ <b>Sent:</b> {active_broadcast.get('sent_count', 0)}\n"
                message += f"❌ <b>Failed:</b> {active_broadcast.get('failed_count', 0)}\n\n"
                message += "Please wait for the current broadcast to complete or send /cancel to stop it."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = "📢 <b>Broadcast Message</b>\n\n"
            message += "Send me the message you want to broadcast to all users.\n\n"
            message += "📝 <b>Supported formats:</b>\n"
            message += "• Text messages with HTML formatting\n"
            message += "• Photos with captions\n"
            message += "• Videos with captions\n"
            message += "• Documents with captions\n"
            message += "• Audio files with captions\n\n"
            message += "Send /cancel to cancel the broadcast."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for broadcast message
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_message')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_text: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_add_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add force sub channel callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # PHP-style message for forwarded message detection
            message = "📢 <b>Add Force Subscription Channel</b>\n\n"
            message += "To add a new force subscription channel:\n\n"
            message += "1️⃣ Go to the channel you want to add\n"
            message += "2️⃣ Forward any message from that channel to this bot\n"
            message += "3️⃣ I will automatically verify and add the channel\n\n"
            message += "⚠️ <b>Requirements:</b>\n"
            message += "• The bot must be an administrator in the channel\n"
            message += "• The channel must be public or the bot must have access\n\n"
            message += "📤 <b>Please forward a message from the channel now:</b>"

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for add force channel
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_force_channel_forward')

        except Exception as e:
            logger.error(f"Error in handle_add_force_sub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove force sub channel callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get current force subscription channels
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()
            channels = await force_sub_service.get_force_subscription_channels()

            if not channels:
                message = "📢 No force subscription channels found.\n\n"
                message += "Use \"➕ Add Force Sub Channel\" to add channels first."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Create removal interface with buttons (like PHP)
            message = "📢 <b>Remove Force Subscription Channel</b>\n\n"
            message += "Select a channel to remove:\n\n"

            keyboard_buttons = []

            for channel in channels:
                channel_title = channel.get('title', 'Unknown Channel')
                channel_id = channel.get('channel_id', channel.get('id', ''))

                keyboard_buttons.append([
                    InlineKeyboardButton(
                        f"🗑️ {channel_title}",
                        callback_data=f"remove_force_sub_{channel_id}"
                    )
                ])

            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)
            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
            message += "• -100*********0\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for remove force channel
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_force_channel')

        except Exception as e:
            logger.error(f"Error in handle_remove_force_sub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_force_sub_channel_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str):
        """Handle confirmation of force sub channel removal"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Remove the channel
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            success = await force_sub_service.remove_force_subscription_channel(channel_id)

            if success:
                await query.answer("✅ Channel removed successfully!", show_alert=True)

                # Refresh the remove interface
                await self.handle_remove_force_sub_channel(update, context)
            else:
                await query.answer("❌ Failed to remove channel. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_remove_force_sub_channel_confirm: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_force_sub_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view force sub channels callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            message = await force_sub_service.format_channels_list_message()
            keyboard = await force_sub_service.create_channels_management_keyboard()

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_view_force_sub_channels: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_withdrawal_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal settings callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Get withdrawal settings
            withdrawal_settings = await admin_service.get_withdrawal_settings(user_id)

            enabled = withdrawal_settings.get('enabled', True)
            tax_type = withdrawal_settings.get('tax_type', 'none')
            tax_amount = withdrawal_settings.get('tax_amount', 0)
            tax_percentage = withdrawal_settings.get('tax_percentage', 0)

            # Format message
            message = "💰 <b>Withdrawal Settings</b>\n\n"
            message += f"📊 <b>Status:</b> {'✅ Enabled' if enabled else '❌ Disabled'}\n\n"

            if tax_type == 'none':
                message += "🏛️ <b>Tax Configuration:</b> No tax applied\n"
            elif tax_type == 'fixed':
                message += f"🏛️ <b>Tax Configuration:</b> Fixed ₹{tax_amount} per withdrawal\n"
            elif tax_type == 'percentage':
                message += f"🏛️ <b>Tax Configuration:</b> {tax_percentage}% of withdrawal amount\n"

            message += "\n📋 <b>Select an option to configure:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    '❌ Disable Withdrawals' if enabled else '✅ Enable Withdrawals',
                    callback_data='toggle_withdrawal_status'
                )],
                [InlineKeyboardButton('🏛️ Configure Tax Settings', callback_data='configure_withdrawal_tax')],
                [InlineKeyboardButton('📊 View Tax Preview', callback_data='preview_withdrawal_tax')],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_settings: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage withdrawals callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Get pending withdrawals
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                message = "💸 <b>Withdrawal Management</b>\n\n"
                message += "✅ No pending withdrawals found.\n\n"
                message += "All withdrawal requests have been processed!"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📊 Withdrawal Statistics', callback_data='withdrawalStats')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Send initial message
            message = "💸 <b>Withdrawal Management</b>\n\n"
            message += f"⏳ Pending Withdrawals: {len(pending_withdrawals)}\n\n"
            message += "Processing withdrawal requests..."

            await query.edit_message_text(message, parse_mode='HTML')

            # Send each withdrawal as a separate message
            for withdrawal in pending_withdrawals:
                user_id_str = str(withdrawal['user_id'])
                amount = withdrawal['amount']
                first_name = withdrawal['first_name']
                withdrawal_method = withdrawal['withdrawal_method']
                account_info = withdrawal['account_info']

                withdrawal_message = f"👤 <b>User:</b> {first_name} (ID: {withdrawal['user_id']})\n"
                withdrawal_message += f"💵 <b>Amount:</b> ₹{amount}\n"
                withdrawal_message += f"🔧 <b>Method:</b> {'USDT (Binance ID)' if withdrawal_method == 'usdt' else 'Bank Account'}\n"
                withdrawal_message += f"📅 <b>Date:</b> {get_current_date()}\n\n"

                if withdrawal_method == 'usdt':
                    binance_id = account_info.get('binance_id', account_info.get('usdt_address', ''))
                    withdrawal_message += f"<b>₿ Binance ID:</b> <code>{binance_id}</code>\n\n"
                else:
                    name = account_info.get('name', '')
                    ifsc = account_info.get('ifsc', '')
                    email = account_info.get('email', '')
                    account_number = account_info.get('account_number', '')
                    mobile_number = account_info.get('mobile_number', '')

                    withdrawal_message += f"<b>👤 Name:</b> {name}\n"
                    withdrawal_message += f"<b>🏛️ IFSC:</b> <code>{ifsc}</code>\n"
                    withdrawal_message += f"<b>📧 Email:</b> {email}\n"
                    withdrawal_message += f"<b>🔢 Account:</b> <code>{account_number}</code>\n"
                    withdrawal_message += f"<b>📱 Mobile:</b> <code>{mobile_number}</code>\n\n"

                withdrawal_message += "Use the buttons below to approve or reject:"

                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('✅ Approve', callback_data=f"approve_withdrawal_{user_id_str}"),
                        InlineKeyboardButton('❌ Reject', callback_data=f"reject_withdrawal_{user_id_str}")
                    ]
                ])

                try:
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=withdrawal_message,
                        parse_mode='HTML',
                        reply_markup=keyboard
                    )
                except Exception as e:
                    logger.error(f"Error sending withdrawal {withdrawal['user_id']}: {e}")

            # Send back button
            back_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await context.bot.send_message(
                chat_id=chat_id,
                text="Use the buttons above to approve or reject withdrawal requests.",
                reply_markup=back_keyboard
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_custom_referral_links(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle custom referral links callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get all custom referrals
            referrals = await custom_referral_service.get_all_custom_referrals()

            # Format message
            message = CustomReferralModel.format_custom_referral_list_message(referrals)
            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_links: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_extra_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle extra rewards callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🎁 <b>Extra Rewards Management</b>\n\n"
            message += "This feature allows you to manage special rewards and bonuses.\n\n"
            message += "Available options:\n"
            message += "• Level-based rewards\n"
            message += "• Special event bonuses\n"
            message += "• Custom reward campaigns\n\n"
            message += "💡 <i>Feature coming soon...</i>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("↩️ Back to Admin Panel", callback_data="admin")]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_extra_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_all_gift_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view all gift codes callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get gift codes from database
            from config.database import get_collection, COLLECTIONS

            gift_codes_collection = await get_collection(COLLECTIONS.get('gift_codes', 'gift_codes'))
            cursor = gift_codes_collection.find({}).sort('created_at', -1).limit(20)
            gift_codes = await cursor.to_list(length=None)

            message = "🎁 <b>Gift Codes Management</b>\n\n"

            if gift_codes:
                message += f"📊 <b>Recent Gift Codes ({len(gift_codes)}):</b>\n\n"
                for i, code in enumerate(gift_codes[:10], 1):
                    status = "✅ Active" if code.get('active', True) else "❌ Inactive"
                    uses = code.get('used_count', 0)
                    max_uses = code.get('max_uses', 'Unlimited')
                    message += f"{i}. <code>{code.get('code', 'N/A')}</code>\n"
                    message += f"   💰 Amount: ₹{code.get('amount', 0)}\n"
                    message += f"   📊 Uses: {uses}/{max_uses}\n"
                    message += f"   📅 Status: {status}\n\n"
            else:
                message += "📭 <i>No gift codes found.</i>\n\n"
                message += "Create gift codes to reward your users!"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("➕ Create Gift Code", callback_data="createGiftCode")],
                [InlineKeyboardButton("🗑️ Delete Gift Code", callback_data="deleteGiftCode")],
                [InlineKeyboardButton("↩️ Back to Admin Panel", callback_data="admin")]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_view_all_gift_codes: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_analytics_overview(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle analytics overview callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get analytics data
            from services.analytics_service import AnalyticsService
            analytics_service = AnalyticsService()

            analytics_data = await analytics_service.get_overview_stats()

            if analytics_data['success']:
                stats = analytics_data['data']

                message = "📊 <b>Analytics Dashboard</b>\n\n"
                message += f"👥 <b>Users:</b>\n"
                message += f"   • Total Users: {stats.get('total_users', 0):,}\n"
                message += f"   • Active Users (24h): {stats.get('active_users_24h', 0):,}\n"
                message += f"   • New Users (24h): {stats.get('new_users_24h', 0):,}\n\n"

                message += f"💰 <b>Financial:</b>\n"
                message += f"   • Total Balance: ₹{stats.get('total_balance', 0):,.2f}\n"
                message += f"   • Total Withdrawals: ₹{stats.get('total_withdrawals', 0):,.2f}\n"
                message += f"   • Pending Withdrawals: {stats.get('pending_withdrawals', 0):,}\n\n"

                message += f"🔗 <b>Referrals:</b>\n"
                message += f"   • Total Referrals: {stats.get('total_referrals', 0):,}\n"
                message += f"   • Referrals (24h): {stats.get('referrals_24h', 0):,}\n\n"

                message += f"📈 <b>Growth Rate:</b>\n"
                growth_rate = stats.get('growth_rate', 0)
                growth_emoji = "📈" if growth_rate > 0 else "📉" if growth_rate < 0 else "➡️"
                message += f"   • {growth_emoji} {growth_rate:+.1f}% (24h)\n\n"

                message += f"🕐 <b>Last Updated:</b> {stats.get('last_updated', 'Just now')}"
            else:
                message = "📊 <b>Analytics Dashboard</b>\n\n"
                message += "❌ <i>Unable to load analytics data.</i>\n"
                message += f"Error: {analytics_data.get('message', 'Unknown error')}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 Refresh", callback_data="analytics_overview")],
                [InlineKeyboardButton("📈 Detailed Stats", callback_data="analytics_detailed")],
                [InlineKeyboardButton("↩️ Back to Admin Panel", callback_data="admin")]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_analytics_overview: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_user_accounts(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage user accounts callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "👥 <b>User Account Management</b>\n\n"
            message += "Manage user withdrawal methods and account details:\n\n"
            message += "🔍 <b>Available Actions:</b>\n"
            message += "• View user withdrawal details\n"
            message += "• Reset user withdrawal method\n"
            message += "• Edit user account information\n\n"
            message += "💡 <i>Enter a user ID to get started.</i>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔍 View User Details", callback_data="viewUserDetails")],
                [InlineKeyboardButton("🔄 Reset User Method", callback_data="resetUserMethod")],
                [InlineKeyboardButton("↩️ Back to Admin Panel", callback_data="admin")]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_manage_user_accounts: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_user_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view user details callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🔍 <b>View User Details</b>\n\n"
            message += "Enter the user ID to view their account details:\n\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for viewing user details
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'view_user_details')

        except Exception as e:
            logger.error(f"Error in handle_view_user_details: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_reset_user_method(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle reset user withdrawal method callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🔄 <b>Reset User Withdrawal Method</b>\n\n"
            message += "Enter the user ID to reset their withdrawal method:\n\n"
            message += "⚠️ <b>Warning:</b> This will allow the user to choose a new withdrawal method.\n\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for resetting user method
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'reset_user_method')

        except Exception as e:
            logger.error(f"Error in handle_reset_user_method: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    # Custom referral helper methods
    async def _show_custom_referral_help(self, update: Update):
        """Show custom referral help (matching PHP version exactly)"""
        from models.custom_referral import CustomReferralModel

        help_text = CustomReferralModel.format_custom_referral_help_message()
        keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

        await update.message.reply_text(help_text, reply_markup=keyboard, parse_mode='HTML')
    
    async def _handle_custom_referral_list(self, update: Update):
        """Handle custom referral list (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get all custom referrals
            referrals = await custom_referral_service.get_all_custom_referrals()

            # Format message
            message = CustomReferralModel.format_custom_referral_list_message(referrals)
            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_list: {e}")
            await update.message.reply_text("❌ Error loading custom referral links.", parse_mode='HTML')

    async def _handle_custom_referral_create(self, update: Update, param: str, user_id_str: str):
        """Handle custom referral create (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Create custom referral
            result = await custom_referral_service.create_custom_referral(param, user_id, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_creation_success_message(
                    result['custom_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_create: {e}")
            await update.message.reply_text("❌ Error creating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_edit(self, update: Update, old_param: str, new_param: str):
        """Handle custom referral edit (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Update custom referral
            result = await custom_referral_service.update_custom_referral(old_param, new_param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_update_success_message(
                    result['old_param'],
                    result['new_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_edit: {e}")
            await update.message.reply_text("❌ Error updating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_delete(self, update: Update, param: str):
        """Handle custom referral delete (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Delete custom referral
            result = await custom_referral_service.delete_custom_referral(param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_deletion_success_message(
                    result['param'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_delete: {e}")
            await update.message.reply_text("❌ Error deleting custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_view(self, update: Update, user_id_str: str):
        """Handle custom referral view (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get custom referrals for user
            referrals = await custom_referral_service.get_custom_referrals_by_user(user_id)

            if not referrals:
                await update.message.reply_text(f"❌ No custom referral links found for user {user_id}.", parse_mode='HTML')
                return

            # Format message for specific user
            message = f"🔗 <b>Custom Referral Links for User {user_id}</b>\n\n"

            for i, referral in enumerate(referrals, 1):
                param = referral.get('custom_param', 'Unknown')
                clicks = referral.get('clicks', 0)
                conversions = referral.get('referrals', 0)
                active = referral.get('active', True)

                status_emoji = "✅" if active else "❌"

                message += f"<b>{i}.</b> {status_emoji} <code>{param}</code>\n"
                message += f"   📊 {clicks} clicks, {conversions} referrals\n\n"

            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_view: {e}")
            await update.message.reply_text("❌ Error viewing custom referral links.", parse_mode='HTML')

    async def handle_analytics_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, action: str):
        """Handle analytics callback actions (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.analytics_service import AnalyticsService
            from models.analytics import AnalyticsModel

            analytics_service = AnalyticsService()

            if action == 'overview':
                # Get comprehensive statistics
                stats = await analytics_service.get_comprehensive_statistics()
                message = AnalyticsModel.format_comprehensive_statistics_message(stats)
                keyboard = AnalyticsModel.create_analytics_dashboard_keyboard()

            elif action == 'daily':
                # Get daily statistics
                daily_stats = await analytics_service.get_daily_statistics(7)
                message = AnalyticsModel.format_daily_statistics_message(daily_stats)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'performers':
                # Get top performers
                performers = await analytics_service.get_top_performers()
                message = AnalyticsModel.format_top_performers_message(performers)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'growth':
                # Get growth metrics
                growth = await analytics_service.get_growth_metrics()
                message = AnalyticsModel.format_growth_metrics_message(growth)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'financial':
                # Get financial statistics
                stats = await analytics_service.get_financial_statistics()
                message = self._format_financial_report(stats)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'tasks':
                # Get task statistics
                stats = await analytics_service.get_task_statistics()
                message = self._format_task_report(stats)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'referrals':
                # Get referral statistics
                stats = await analytics_service.get_referral_statistics()
                message = self._format_referral_report(stats)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'system':
                # Get system statistics
                stats = await analytics_service.get_system_statistics()
                message = self._format_system_report(stats)
                keyboard = AnalyticsModel.create_analytics_navigation_keyboard()

            elif action == 'refresh':
                # Refresh overview
                stats = await analytics_service.get_comprehensive_statistics()
                message = AnalyticsModel.format_comprehensive_statistics_message(stats)
                keyboard = AnalyticsModel.create_analytics_dashboard_keyboard()
                await query.answer("✅ Data refreshed!", show_alert=False)

            else:
                await query.answer("❌ Unknown analytics action.", show_alert=True)
                return

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_analytics_callback: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    def _format_financial_report(self, stats: Dict[str, Any]) -> str:
        """Format financial report (matching PHP version exactly)"""
        message = "💰 <b>Financial Report</b>\n\n"
        message += f"💳 <b>Total Balance in System:</b> ₹{stats.get('total_balance', 0):,.2f}\n\n"

        message += f"📊 <b>Withdrawal Statistics:</b>\n"
        message += f"• Total Requests: <b>{stats.get('total_withdrawals', 0):,}</b>\n"
        message += f"• Pending: <b>{stats.get('pending_withdrawals', 0):,}</b> (₹{stats.get('pending_amount', 0):,.2f})\n"
        message += f"• Approved: <b>{stats.get('approved_withdrawals', 0):,}</b>\n"
        message += f"• Rejected: <b>{stats.get('rejected_withdrawals', 0):,}</b>\n"
        message += f"• Total Paid: <b>₹{stats.get('total_withdrawn', 0):,.2f}</b>\n"
        message += f"• Today's Requests: <b>{stats.get('withdrawals_today', 0):,}</b>\n\n"

        # Calculate approval rate
        total_processed = stats.get('approved_withdrawals', 0) + stats.get('rejected_withdrawals', 0)
        if total_processed > 0:
            approval_rate = (stats.get('approved_withdrawals', 0) / total_processed) * 100
            message += f"📈 <b>Approval Rate:</b> {approval_rate:.1f}%"

        return message

    def _format_task_report(self, stats: Dict[str, Any]) -> str:
        """Format task report (matching PHP version exactly)"""
        message = "📋 <b>Task Management Report</b>\n\n"
        message += f"📝 <b>Task Statistics:</b>\n"
        message += f"• Total Tasks: <b>{stats.get('total_tasks', 0):,}</b>\n"
        message += f"• Active Tasks: <b>{stats.get('active_tasks', 0):,}</b>\n\n"

        message += f"📊 <b>Submission Statistics:</b>\n"
        message += f"• Total Submissions: <b>{stats.get('total_submissions', 0):,}</b>\n"
        message += f"• Pending Review: <b>{stats.get('pending_submissions', 0):,}</b>\n"
        message += f"• Approved: <b>{stats.get('approved_submissions', 0):,}</b>\n"
        message += f"• Rejected: <b>{stats.get('rejected_submissions', 0):,}</b>\n"
        message += f"• Today's Submissions: <b>{stats.get('submissions_today', 0):,}</b>\n\n"

        # Calculate approval rate
        total_reviewed = stats.get('approved_submissions', 0) + stats.get('rejected_submissions', 0)
        if total_reviewed > 0:
            approval_rate = (stats.get('approved_submissions', 0) / total_reviewed) * 100
            message += f"📈 <b>Approval Rate:</b> {approval_rate:.1f}%"

        return message

    def _format_referral_report(self, stats: Dict[str, Any]) -> str:
        """Format referral report (matching PHP version exactly)"""
        message = "🔗 <b>Referral System Report</b>\n\n"
        message += f"👥 <b>Regular Referrals:</b>\n"
        message += f"• Total Referrals: <b>{stats.get('total_referrals', 0):,}</b>\n"
        message += f"• Users with Referrals: <b>{stats.get('users_with_referrals', 0):,}</b>\n\n"

        message += f"🔗 <b>Custom Referral Links:</b>\n"
        message += f"• Total Links: <b>{stats.get('total_custom_referrals', 0):,}</b>\n"
        message += f"• Active Links: <b>{stats.get('active_custom_referrals', 0):,}</b>\n"
        message += f"• Total Clicks: <b>{stats.get('total_custom_clicks', 0):,}</b>\n"
        message += f"• Total Conversions: <b>{stats.get('total_custom_conversions', 0):,}</b>\n\n"

        # Calculate conversion rate
        total_clicks = stats.get('total_custom_clicks', 0)
        total_conversions = stats.get('total_custom_conversions', 0)
        if total_clicks > 0:
            conversion_rate = (total_conversions / total_clicks) * 100
            message += f"📈 <b>Conversion Rate:</b> {conversion_rate:.1f}%"

        return message

    def _format_system_report(self, stats: Dict[str, Any]) -> str:
        """Format system report (matching PHP version exactly)"""
        message = "⚙️ <b>System Status Report</b>\n\n"
        message += f"🔔 <b>Force Subscription:</b>\n"
        message += f"• Configured Channels: <b>{stats.get('total_force_channels', 0):,}</b>\n\n"

        message += f"🎁 <b>Gift Code System:</b>\n"
        message += f"• Total Gift Codes: <b>{stats.get('total_gift_codes', 0):,}</b>\n"
        message += f"• Active Codes: <b>{stats.get('active_gift_codes', 0):,}</b>\n\n"

        message += f"👨‍💼 <b>Admin Activity:</b>\n"
        message += f"• Total Admin Actions: <b>{stats.get('total_admin_actions', 0):,}</b>\n"
        message += f"• Actions Today: <b>{stats.get('admin_actions_today', 0):,}</b>\n\n"

        # Last updated
        last_updated = stats.get('last_updated', 0)
        if last_updated:
            from datetime import datetime
            update_time = datetime.fromtimestamp(last_updated).strftime('%Y-%m-%d %H:%M:%S')
            message += f"🕒 <i>Last updated: {update_time}</i>"

        return message
    
    # Additional placeholder methods for other admin handlers
    async def handle_toggle_withdrawal_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle toggle withdrawal status (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Get current settings
            withdrawal_settings = await admin_service.get_withdrawal_settings(user_id)
            current_status = withdrawal_settings.get('enabled', True)
            new_status = not current_status

            # Update settings
            withdrawal_settings['enabled'] = new_status
            success = await admin_service.update_withdrawal_settings(user_id, withdrawal_settings)

            if success:
                status_text = "enabled" if new_status else "disabled"
                await query.answer(f"✅ Withdrawals {status_text} successfully!", show_alert=True)
                # Refresh the withdrawal settings page
                await self.handle_withdrawal_settings(update, context)
            else:
                await query.answer("❌ Error updating withdrawal status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_withdrawal_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_configure_withdrawal_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure withdrawal tax (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🏛️ <b>Configure Withdrawal Tax</b>\n\n"
            message += "Select the type of tax to apply on withdrawals:\n\n"
            message += "• <b>No Tax:</b> Users receive full withdrawal amount\n"
            message += "• <b>Fixed Tax:</b> Deduct a fixed amount from each withdrawal\n"
            message += "• <b>Percentage Tax:</b> Deduct a percentage from each withdrawal"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ No Tax', callback_data='set_tax_none')],
                [InlineKeyboardButton('💰 Fixed Tax', callback_data='set_tax_fixed')],
                [InlineKeyboardButton('📊 Percentage Tax', callback_data='set_tax_percentage')],
                [InlineKeyboardButton('↩️ Back to Settings', callback_data='withdrawal_settings')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_configure_withdrawal_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_tax_type(self, update: Update, context: ContextTypes.DEFAULT_TYPE, tax_type: str):
        """Handle set tax type (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            if tax_type == 'none':
                # Set no tax
                withdrawal_settings = await admin_service.get_withdrawal_settings(user_id)
                withdrawal_settings['tax_type'] = 'none'
                withdrawal_settings['tax_amount'] = 0
                withdrawal_settings['tax_percentage'] = 0

                success = await admin_service.update_withdrawal_settings(user_id, withdrawal_settings)

                if success:
                    await query.answer("✅ Tax disabled successfully!", show_alert=True)
                    await self.handle_withdrawal_settings(update, context)
                else:
                    await query.answer("❌ Error updating tax settings.", show_alert=True)

            elif tax_type == 'fixed':
                # Ask for fixed amount
                message = "💰 <b>Set Fixed Tax Amount</b>\n\n"
                message += "Enter the fixed tax amount to deduct from each withdrawal:\n\n"
                message += "Example: Enter <code>10</code> to deduct ₹10 from each withdrawal\n\n"
                message += "Send /cancel to cancel the process."

                await query.edit_message_text(message, parse_mode='HTML')

                # Set session for next step
                from handlers.session_handlers import SessionHandlers
                session_handlers = SessionHandlers()
                await session_handlers.set_user_session(user_id, 'set_withdrawal_tax', {'type': 'fixed'})

            elif tax_type == 'percentage':
                # Ask for percentage
                message = "📊 <b>Set Percentage Tax</b>\n\n"
                message += "Enter the percentage to deduct from each withdrawal:\n\n"
                message += "Example: Enter <code>5</code> to deduct 5% from each withdrawal\n\n"
                message += "Send /cancel to cancel the process."

                await query.edit_message_text(message, parse_mode='HTML')

                # Set session for next step
                from handlers.session_handlers import SessionHandlers
                session_handlers = SessionHandlers()
                await session_handlers.set_user_session(user_id, 'set_withdrawal_tax', {'type': 'percentage'})

        except Exception as e:
            logger.error(f"Error in handle_set_tax_type: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_withdrawal_tax_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, session_data: Dict[str, Any]):
        """Handle withdrawal tax configuration step 2 (matching PHP version exactly)"""
        user_id = update.message.from_user.id
        chat_id = update.message.chat_id

        try:
            if not is_admin(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            tax_type = session_data.get('type')

            try:
                amount = float(text.strip())
                if amount < 0:
                    raise ValueError("Amount cannot be negative")
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid Amount</b>\n\nPlease enter a valid positive number.",
                    parse_mode='HTML'
                )
                return

            # Update withdrawal settings
            withdrawal_settings = await admin_service.get_withdrawal_settings(user_id)
            withdrawal_settings['tax_type'] = tax_type

            if tax_type == 'fixed':
                withdrawal_settings['tax_amount'] = amount
                withdrawal_settings['tax_percentage'] = 0
                success_message = f"✅ <b>Fixed Tax Set Successfully!</b>\n\n💰 <b>Tax Amount:</b> ₹{amount} per withdrawal"
            else:  # percentage
                withdrawal_settings['tax_percentage'] = amount
                withdrawal_settings['tax_amount'] = 0
                success_message = f"✅ <b>Percentage Tax Set Successfully!</b>\n\n📊 <b>Tax Rate:</b> {amount}% of withdrawal amount"

            success = await admin_service.update_withdrawal_settings(user_id, withdrawal_settings)

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Withdrawal Settings', callback_data='withdrawal_settings')],
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ <b>Error</b>\n\nFailed to update tax settings. Please try again.",
                    parse_mode='HTML'
                )

            # Clear session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in handle_set_withdrawal_tax_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    async def handle_set_tax_type(self, update: Update, context: ContextTypes.DEFAULT_TYPE, tax_type: str):
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def handle_preview_withdrawal_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def handle_manage_tasks(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage tasks callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            all_tasks = await task_service.get_all_tasks()

            if not all_tasks:
                message = "📋 <b>Task Management</b>\n\n"
                message += "❌ No tasks found.\n\n"
                message += "Use \"➕ Add New Task\" to create your first task."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = "📋 <b>Task Management</b>\n\n"
            message += f"Total Tasks: {len(all_tasks)}\n\n"

            keyboard_buttons = []

            for task in all_tasks:
                status_icon = '✅' if task['status'] == 'active' else '❌'
                task_name = task['name'][:20] + '...' if len(task['name']) > 20 else task['name']

                keyboard_buttons.append([
                    InlineKeyboardButton(
                        f"{status_icon} {task_name} - ₹{task['reward_amount']}",
                        callback_data=f"editTask_{task['task_id']}"
                    )
                ])

            keyboard_buttons.extend([
                [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                [InlineKeyboardButton('📊 View Pending Submissions', callback_data='viewPendingSubmissions')],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_manage_tasks: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_new_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add new task callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "➕ <b>Add New Task</b>\n\n"
            message += "Let's create a new task for users to complete.\n\n"
            message += "📝 <b>Step 1:</b> Enter the task name\n"
            message += "Keep it short and descriptive (max 50 characters).\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_task_name')

        except Exception as e:
            logger.error(f"Error in handle_add_new_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_generate_gift_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle generate gift code callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from models.gift_code import GiftCodeModel

            message = GiftCodeModel.format_gift_code_generation_step1_message()

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for gift code generation
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'generate_gift_code')

        except Exception as e:
            logger.error(f"Error in handle_generate_gift_code: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_pending_submissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view pending submissions callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            pending_submissions = await task_service.get_pending_task_submissions()

            if not pending_submissions:
                message = "📊 <b>Pending Task Submissions</b>\n\n"
                message += "✅ No pending submissions found.\n\n"
                message += "All tasks have been reviewed!"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manageTasks')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Send initial message
            message = "📊 <b>Pending Task Submissions</b>\n\n"
            message += f"Total Pending: {len(pending_submissions)}\n\n"

            await query.edit_message_text(message, parse_mode='HTML')

            # Send each submission as a separate message
            from services.user_service import UserService
            user_service = UserService()

            for submission in pending_submissions:
                task = await task_service.get_task_by_id(submission['task_id'])
                user = await user_service.get_user(submission['user_id'])

                if not task or not user:
                    continue

                submission_message = f"""👤 <b>User:</b> {user['first_name']} (ID: {submission['user_id']})
📋 <b>Task:</b> {task['name']}
💰 <b>Reward:</b> ₹{task['reward_amount']}
📅 <b>Submitted:</b> {get_current_date()}
🆔 <b>Submission ID:</b> {submission['submission_id']}"""

                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('✅ Approve', callback_data=f"approveTask_{submission['submission_id']}"),
                        InlineKeyboardButton('❌ Reject', callback_data=f"rejectTask_{submission['submission_id']}")
                    ]
                ])

                # Try to send the submitted file with the message
                try:
                    file_id = submission.get('file_id')
                    if file_id:
                        if file_id.startswith('AgAC'):  # Photo
                            await context.bot.send_photo(
                                chat_id=chat_id,
                                photo=file_id,
                                caption=submission_message,
                                parse_mode='HTML',
                                reply_markup=keyboard
                            )
                        else:  # Document
                            await context.bot.send_document(
                                chat_id=chat_id,
                                document=file_id,
                                caption=submission_message,
                                parse_mode='HTML',
                                reply_markup=keyboard
                            )
                    else:
                        await context.bot.send_message(
                            chat_id=chat_id,
                            text=submission_message,
                            parse_mode='HTML',
                            reply_markup=keyboard
                        )
                except Exception as e:
                    logger.error(f"Error sending submission {submission['submission_id']}: {e}")
                    # Fallback to text message
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=submission_message,
                        parse_mode='HTML',
                        reply_markup=keyboard
                    )

            # Send back button
            back_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manageTasks')]
            ])

            await context.bot.send_message(
                chat_id=chat_id,
                text="Use the buttons above to approve or reject submissions.",
                reply_markup=back_keyboard
            )

        except Exception as e:
            logger.error(f"Error in handle_view_pending_submissions: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_configure_level_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure level rewards callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.level_rewards_service import LevelRewardsService
            from models.level_rewards import LevelRewardsModel

            level_rewards_service = LevelRewardsService()

            config = await level_rewards_service.get_level_rewards_config()
            enabled = await level_rewards_service.is_level_rewards_enabled()

            message = LevelRewardsModel.format_admin_config_message(config, enabled)
            keyboard = LevelRewardsModel.create_admin_config_keyboard(enabled)

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_configure_level_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle toggle level bonus callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.level_rewards_service import LevelRewardsService
            from models.level_rewards import LevelRewardsModel

            level_rewards_service = LevelRewardsService()

            # Get current status and toggle
            current_status = await level_rewards_service.is_level_rewards_enabled()
            new_status = not current_status

            if await level_rewards_service.toggle_level_rewards(new_status):
                config = await level_rewards_service.get_level_rewards_config()

                message = LevelRewardsModel.format_toggle_success_message(new_status, config)

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏆 Configure Again', callback_data='configureLevelRewards')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

                action_text = "enabled" if new_status else "disabled"
                await query.answer(f"✅ Level rewards system {action_text}!", show_alert=True)
            else:
                await query.answer("❌ Error updating level rewards system.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_level_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_custom_referral_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, action: str):
        """Handle custom referral callback actions (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            if action == 'list':
                from services.custom_referral_service import CustomReferralService
                from models.custom_referral import CustomReferralModel

                custom_referral_service = CustomReferralService()

                # Get all custom referrals
                referrals = await custom_referral_service.get_all_custom_referrals()

                # Format message
                message = CustomReferralModel.format_custom_referral_list_message(referrals)
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            elif action == 'help':
                from models.custom_referral import CustomReferralModel

                help_text = CustomReferralModel.format_custom_referral_help_message()
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(help_text, reply_markup=keyboard, parse_mode='HTML')

            else:
                await query.answer("❌ Unknown action.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_callback: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_remove_force_sub_channel_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str):
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def handle_approve_task_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, submission_id: str):
        """Handle approve task submission (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            # Get submission details
            submission = await task_service.get_submission_by_id(submission_id)
            if not submission:
                await query.answer("❌ Submission not found.", show_alert=True)
                return

            if submission['status'] != 'pending':
                await query.answer("❌ This submission has already been reviewed.", show_alert=True)
                return

            # Approve the submission
            if await task_service.approve_task_submission(submission_id):
                # Update the message to show approval
                try:
                    approved_message = query.message.caption or query.message.text
                    approved_message += "\n\n✅ <b>APPROVED</b> by admin"

                    if query.message.photo:
                        await query.message.edit_caption(
                            caption=approved_message,
                            parse_mode='HTML'
                        )
                    elif query.message.document:
                        await query.message.edit_caption(
                            caption=approved_message,
                            parse_mode='HTML'
                        )
                    else:
                        await query.edit_message_text(
                            approved_message,
                            parse_mode='HTML'
                        )
                except Exception as e:
                    logger.error(f"Error updating approval message: {e}")

                await query.answer("✅ Task submission approved successfully!", show_alert=True)

            else:
                await query.answer("❌ Failed to approve submission. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_approve_task_submission: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_reject_task_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, submission_id: str):
        """Handle reject task submission (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            # Get submission details
            submission = await task_service.get_submission_by_id(submission_id)
            if not submission:
                await query.answer("❌ Submission not found.", show_alert=True)
                return

            if submission['status'] != 'pending':
                await query.answer("❌ This submission has already been reviewed.", show_alert=True)
                return

            # Reject the submission
            admin_note = "Submission did not meet requirements"
            if await task_service.reject_task_submission(submission_id, admin_note):
                # Update the message to show rejection
                try:
                    rejected_message = query.message.caption or query.message.text
                    rejected_message += "\n\n❌ <b>REJECTED</b> by admin"

                    if query.message.photo:
                        await query.message.edit_caption(
                            caption=rejected_message,
                            parse_mode='HTML'
                        )
                    elif query.message.document:
                        await query.message.edit_caption(
                            caption=rejected_message,
                            parse_mode='HTML'
                        )
                    else:
                        await query.edit_message_text(
                            rejected_message,
                            parse_mode='HTML'
                        )
                except Exception as e:
                    logger.error(f"Error updating rejection message: {e}")

                await query.answer("❌ Task submission rejected.", show_alert=True)

            else:
                await query.answer("❌ Failed to reject submission. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_reject_task_submission: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def handle_add_task_step6(self, update: Update, context: ContextTypes.DEFAULT_TYPE, status: str):
        """Handle add task step 6 - final creation (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get session data
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            session = await session_handlers.get_user_session(user_id)

            if not session or session.get('step') != 'add_task_confirm':
                await query.answer("❌ Session expired. Please start again.", show_alert=True)
                return

            data = session.get('data', {})
            task_name = data.get('task_name', '')
            task_description = data.get('task_description', '')
            reward_amount = data.get('reward_amount', 0)
            media_url = data.get('media_url', '')

            if not task_name or not task_description or reward_amount <= 0:
                await query.answer("❌ Invalid task data. Please start again.", show_alert=True)
                await session_handlers.clear_user_session(user_id)
                return

            # Create the task
            from services.task_service import TaskService
            from models.task import TaskModel

            task_service = TaskService()

            task_data = TaskModel.create_new_task(
                name=task_name,
                description=task_description,
                reward_amount=reward_amount,
                media_url=media_url,
                status=status,
                created_by=user_id
            )

            if await task_service.add_task(task_data):
                # Clear session
                await session_handlers.clear_user_session(user_id)

                # Show success message
                success_message = f"""✅ <b>Task Created Successfully!</b>

📝 <b>Name:</b> {task_name}
📋 <b>Description:</b> {task_description}
💰 <b>Reward:</b> ₹{reward_amount}
📸 <b>Media:</b> {'Yes' if media_url else 'No'}
📊 <b>Status:</b> {status.title()}

The task is now {'available to users' if status == 'active' else 'hidden from users'}."""

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')],
                    [InlineKeyboardButton('➕ Add Another Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("✅ Task created successfully!", show_alert=True)

            else:
                await query.answer("❌ Failed to create task. Please try again.", show_alert=True)
                await session_handlers.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in handle_add_task_step6: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    # Session step handlers (placeholders)
    async def handle_add_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await update.message.reply_text("🚧 Feature coming soon!")
    
    async def handle_add_balance_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        await update.message.reply_text("🚧 Feature coming soon!")
    
    async def handle_remove_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await update.message.reply_text("🚧 Feature coming soon!")
    
    async def handle_remove_balance_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        await update.message.reply_text("🚧 Feature coming soon!")
    
    async def handle_ban_user_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await update.message.reply_text("🚧 Feature coming soon!")
    
    async def handle_unban_user_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        await update.message.reply_text("🚧 Feature coming soon!")
