<?php
/**
 * Web Admin Panel Configuration - Optimized for Large User Base
 * Secure configuration for the web-based admin panel with performance optimizations
 */

// Start session for authentication with custom settings
ini_set('session.gc_maxlifetime', 3600);
session_start();

// Set memory and time limits for admin panel
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 120);

// Include main bot configuration
require_once '../config.php';
require_once '../core_functions.php';
require_once '../storage_abstraction.php';
require_once '../database_functions.php';

// Web Admin Configuration
define('WEB_ADMIN_LOGIN_CODE', '1412');
define('WEB_ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
define('WEB_ADMIN_USERS_PER_PAGE', 50); // Pagination for better performance
define('WEB_ADMIN_MAX_SEARCH_RESULTS', 200); // Limit search results

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

/**
 * Check if user is authenticated
 */
function isAuthenticated() {
    return isset($_SESSION['web_admin_authenticated']) && 
           $_SESSION['web_admin_authenticated'] === true &&
           isset($_SESSION['web_admin_login_time']) &&
           (time() - $_SESSION['web_admin_login_time']) < WEB_ADMIN_SESSION_TIMEOUT;
}

/**
 * Authenticate user with login code
 */
function authenticate($loginCode) {
    if ($loginCode === WEB_ADMIN_LOGIN_CODE) {
        $_SESSION['web_admin_authenticated'] = true;
        $_SESSION['web_admin_login_time'] = time();
        return true;
    }
    return false;
}

/**
 * Logout user
 */
function logout() {
    unset($_SESSION['web_admin_authenticated']);
    unset($_SESSION['web_admin_login_time']);
    session_destroy();
}

/**
 * Redirect to login if not authenticated
 */
function requireAuth() {
    if (!isAuthenticated()) {
        header('Location: index.php?action=login');
        exit;
    }
}

/**
 * Get paginated users with detailed information - OPTIMIZED for large datasets
 */
function getUsersPaginated($page = 1, $perPage = 50, $search = '', $filter = 'all', $referralFilter = 'all') {
    $users = [];
    $totalUsers = 0;
    $offset = ($page - 1) * $perPage;

    if (STORAGE_MODE === 'json') {
        // For JSON mode, we need to optimize by reading the file once and processing efficiently
        $usersData = readJsonFile(USERS_FILE);
        $totalUsers = count($usersData);

        // Apply filters first to reduce processing
        $filteredUsers = [];
        foreach ($usersData as $userId => $userData) {
            // Apply search filter early
            if (!empty($search)) {
                $searchMatch = stripos($userData['first_name'] ?? '', $search) !== false ||
                              stripos($userData['username'] ?? '', $search) !== false ||
                              stripos($userId, $search) !== false;
                if (!$searchMatch) continue;
            }

            // Apply status filter
            if ($filter !== 'all') {
                $filterMatch = false;
                switch ($filter) {
                    case 'banned':
                        $filterMatch = ($userData['banned'] ?? false);
                        break;
                    case 'active':
                        $filterMatch = !($userData['banned'] ?? false);
                        break;
                    case 'high_balance':
                        $filterMatch = ($userData['balance'] ?? 0) > 100;
                        break;
                    case 'pending_withdrawal':
                        $filterMatch = ($userData['withdraw_under_review'] ?? 0) > 0;
                        break;
                    default:
                        $filterMatch = true;
                }
                if (!$filterMatch) continue;
            }

            // Apply referral filter
            if ($referralFilter !== 'all') {
                $referralMatch = false;
                switch ($referralFilter) {
                    case 'has_referrer':
                        $referralMatch = ($userData['referred_by'] ?? 'None') !== 'None' && !empty($userData['referred_by']);
                        break;
                    case 'no_referrer':
                        $referralMatch = ($userData['referred_by'] ?? 'None') === 'None' || empty($userData['referred_by']);
                        break;
                    case 'has_referrals':
                        $referralMatch = count($userData['promotion_report'] ?? []) > 0;
                        break;
                    case 'no_referrals':
                        $referralMatch = count($userData['promotion_report'] ?? []) == 0;
                        break;
                    case 'high_referrals':
                        $referralMatch = count($userData['promotion_report'] ?? []) >= 5;
                        break;
                    case 'top_earners':
                        $referralMatch = calculateReferralEarnings($userData['promotion_report'] ?? []) > 50;
                        break;
                    default:
                        $referralMatch = true;
                }
                if (!$referralMatch) continue;
            }

            $filteredUsers[$userId] = $userData;
        }

        // Update total count after filtering
        $totalUsers = count($filteredUsers);

        // Apply pagination to filtered results
        $paginatedUsers = array_slice($filteredUsers, $offset, $perPage, true);

        // Format only the paginated users
        foreach ($paginatedUsers as $userId => $userData) {
            $user = formatUserData($userId, $userData);
            // Only get referrer info for displayed users
            $user['referrer_info'] = getReferrerInfo($userData['referred_by'] ?? 'None');
            $users[] = $user;
        }

    } else {
        // MySQL implementation with proper pagination
        try {
            $pdo = getDB();

            // Build WHERE clause for filters
            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = "(u.first_name LIKE ? OR u.username LIKE ? OR u.user_id LIKE ?)";
                $searchParam = "%{$search}%";
                $params[] = $searchParam;
                $params[] = $searchParam;
                $params[] = $searchParam;
            }

            if ($filter !== 'all') {
                switch ($filter) {
                    case 'banned':
                        $whereConditions[] = "u.banned = TRUE";
                        break;
                    case 'active':
                        $whereConditions[] = "u.banned = FALSE";
                        break;
                    case 'high_balance':
                        $whereConditions[] = "u.balance > 100";
                        break;
                    case 'pending_withdrawal':
                        $whereConditions[] = "u.withdraw_under_review > 0";
                        break;
                }
            }

            $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

            // Get total count
            $countQuery = "SELECT COUNT(*) as total FROM users u {$whereClause}";
            $stmt = $pdo->prepare($countQuery);
            $stmt->execute($params);
            $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Get paginated results
            $query = "
                SELECT u.*,
                       ua.name, ua.ifsc, ua.email, ua.account_number, ua.mobile_number,
                       COUNT(pr.id) as total_referrals,
                       COALESCE(SUM(CASE WHEN wr.status = 'Passed' THEN wr.amount ELSE 0 END), 0) as total_withdrawals,
                       COALESCE(SUM(CASE WHEN wr.status = 'Under review' THEN wr.amount ELSE 0 END), 0) as pending_withdrawals,
                       COALESCE(SUM(pr.amount_got), 0) as referral_earnings,
                       ref.first_name as referrer_name,
                       ref.username as referrer_username
                FROM users u
                LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
                LEFT JOIN promotion_reports pr ON u.user_id = pr.referrer_id
                LEFT JOIN withdrawal_reports wr ON u.user_id = wr.user_id
                LEFT JOIN users ref ON u.referred_by = ref.user_id
                {$whereClause}
                GROUP BY u.user_id
                ORDER BY u.created_at DESC
                LIMIT ? OFFSET ?
            ";

            $params[] = $perPage;
            $params[] = $offset;

            $stmt = $pdo->prepare($query);
            $stmt->execute($params);

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // Add referrer info for MySQL
                if ($row['referred_by'] !== 'None' && !empty($row['referrer_name'])) {
                    $row['referrer_info'] = [
                        'user_id' => $row['referred_by'],
                        'first_name' => $row['referrer_name'],
                        'username' => $row['referrer_username'],
                        'referral_date' => $row['created_at']
                    ];
                } else {
                    $row['referrer_info'] = null;
                }
                $users[] = $row;
            }
        } catch (Exception $e) {
            error_log("Error fetching paginated users: " . $e->getMessage());
        }
    }

    return [
        'users' => $users,
        'total' => $totalUsers,
        'page' => $page,
        'perPage' => $perPage,
        'totalPages' => ceil($totalUsers / $perPage)
    ];
}

/**
 * Get basic user count for dashboard - OPTIMIZED
 */
function getUserCount() {
    if (STORAGE_MODE === 'json') {
        $usersData = readJsonFile(USERS_FILE);
        return count($usersData);
    } else {
        try {
            $pdo = getDB();
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting user count: " . $e->getMessage());
            return 0;
        }
    }
}

/**
 * Format user data for consistent display
 */
function formatUserData($userId, $userData) {
    return [
        'user_id' => $userId,
        'first_name' => $userData['first_name'] ?? '',
        'username' => $userData['username'] ?? '',
        'balance' => $userData['balance'] ?? 0,
        'successful_withdraw' => $userData['successful_withdraw'] ?? 0,
        'withdraw_under_review' => $userData['withdraw_under_review'] ?? 0,
        'banned' => $userData['banned'] ?? false,
        'referred_by' => $userData['referred_by'] ?? 'None',
        'total_referrals' => count($userData['promotion_report'] ?? []),
        'referrals' => $userData['promotion_report'] ?? [],
        'withdrawal_history' => $userData['withdrawal_report'] ?? [],
        'created_at' => $userData['created_at'] ?? date('Y-m-d H:i:s'),
        'last_activity' => $userData['last_activity'] ?? date('Y-m-d H:i:s'),
        'referrer_info' => null, // Will be populated by getReferrerInfo
        'referral_earnings' => calculateReferralEarnings($userData['promotion_report'] ?? [])
    ];
}

/**
 * Get detailed referrer information for a user
 */
function getReferrerInfo($referredBy) {
    if ($referredBy === 'None' || empty($referredBy)) {
        return null;
    }

    if (STORAGE_MODE === 'json') {
        $usersData = readJsonFile(USERS_FILE);
        if (isset($usersData[$referredBy])) {
            return [
                'user_id' => $referredBy,
                'first_name' => $usersData[$referredBy]['first_name'] ?? '',
                'username' => $usersData[$referredBy]['username'] ?? '',
                'referral_date' => $usersData[$referredBy]['created_at'] ?? ''
            ];
        }
    } else {
        try {
            $pdo = getDB();
            $stmt = $pdo->prepare("SELECT user_id, first_name, username, created_at FROM users WHERE user_id = ?");
            $stmt->execute([$referredBy]);
            $referrer = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($referrer) {
                return [
                    'user_id' => $referrer['user_id'],
                    'first_name' => $referrer['first_name'],
                    'username' => $referrer['username'],
                    'referral_date' => $referrer['created_at']
                ];
            }
        } catch (Exception $e) {
            error_log("Error fetching referrer info: " . $e->getMessage());
        }
    }

    return null;
}

/**
 * Calculate total earnings from referrals
 */
function calculateReferralEarnings($promotionReports) {
    $total = 0;
    foreach ($promotionReports as $report) {
        $total += $report['amount_got'] ?? 0;
    }
    return $total;
}

/**
 * Get referral chain for a user (both up and down the chain)
 */
function getReferralChain($userId) {
    $chain = [
        'upline' => [],
        'downline' => [],
        'user' => null
    ];

    if (STORAGE_MODE === 'json') {
        $usersData = readJsonFile(USERS_FILE);

        // Get current user
        if (isset($usersData[$userId])) {
            $chain['user'] = formatUserData($userId, $usersData[$userId]);

            // Get upline (who referred this user)
            $referredBy = $usersData[$userId]['referred_by'] ?? 'None';
            if ($referredBy !== 'None' && isset($usersData[$referredBy])) {
                $chain['upline'][] = formatUserData($referredBy, $usersData[$referredBy]);

                // Get upline's upline (2 levels up)
                $upperReferredBy = $usersData[$referredBy]['referred_by'] ?? 'None';
                if ($upperReferredBy !== 'None' && isset($usersData[$upperReferredBy])) {
                    $chain['upline'][] = formatUserData($upperReferredBy, $usersData[$upperReferredBy]);
                }
            }

            // Get downline (users referred by this user)
            $promotionReports = $usersData[$userId]['promotion_report'] ?? [];
            foreach ($promotionReports as $report) {
                $referredUserId = $report['referred_user_id'];
                if (isset($usersData[$referredUserId])) {
                    $referredUser = formatUserData($referredUserId, $usersData[$referredUserId]);
                    $referredUser['referral_date'] = $report['date'] ?? '';
                    $referredUser['commission_earned'] = $report['amount_got'] ?? 0;
                    $chain['downline'][] = $referredUser;
                }
            }
        }
    } else {
        try {
            $pdo = getDB();

            // Get current user
            $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                $chain['user'] = $user;

                // Get upline
                if ($user['referred_by'] !== 'None') {
                    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
                    $stmt->execute([$user['referred_by']]);
                    $referrer = $stmt->fetch(PDO::FETCH_ASSOC);
                    if ($referrer) {
                        $chain['upline'][] = $referrer;

                        // Get upline's upline
                        if ($referrer['referred_by'] !== 'None') {
                            $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
                            $stmt->execute([$referrer['referred_by']]);
                            $upperReferrer = $stmt->fetch(PDO::FETCH_ASSOC);
                            if ($upperReferrer) {
                                $chain['upline'][] = $upperReferrer;
                            }
                        }
                    }
                }

                // Get downline
                $stmt = $pdo->prepare("
                    SELECT u.*, pr.amount_got as commission_earned, pr.created_at as referral_date
                    FROM users u
                    JOIN promotion_reports pr ON u.user_id = pr.referred_user_id
                    WHERE pr.referrer_id = ?
                    ORDER BY pr.created_at DESC
                ");
                $stmt->execute([$userId]);
                $chain['downline'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } catch (Exception $e) {
            error_log("Error fetching referral chain: " . $e->getMessage());
        }
    }

    return $chain;
}

/**
 * Get financial overview statistics - OPTIMIZED
 */
function getFinancialOverview() {
    $stats = [
        'total_withdrawals' => 0,
        'pending_withdrawals' => 0,
        'total_user_balances' => 0,
        'total_users' => 0,
        'commission_earned' => 0
    ];

    if (STORAGE_MODE === 'json') {
        // Use optimized approach for JSON - read file once and calculate
        $usersData = readJsonFile(USERS_FILE);
        $stats['total_users'] = count($usersData);

        // Calculate financial stats efficiently
        foreach ($usersData as $userData) {
            $stats['total_withdrawals'] += $userData['successful_withdraw'] ?? 0;
            $stats['pending_withdrawals'] += $userData['withdraw_under_review'] ?? 0;
            $stats['total_user_balances'] += $userData['balance'] ?? 0;
        }
    } else {
        try {
            $pdo = getDB();
            $stmt = $pdo->query("
                SELECT
                    COUNT(*) as total_users,
                    COALESCE(SUM(balance), 0) as total_user_balances,
                    COALESCE(SUM(successful_withdraw), 0) as total_withdrawals,
                    COALESCE(SUM(withdraw_under_review), 0) as pending_withdrawals
                FROM users
            ");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) {
                $stats = array_merge($stats, $result);
            }
        } catch (Exception $e) {
            error_log("Error fetching financial overview: " . $e->getMessage());
        }
    }

    // Calculate commission (assuming 10% commission on withdrawals)
    $stats['commission_earned'] = $stats['total_withdrawals'] * 0.1;

    return $stats;
}

/**
 * Get bot statistics - OPTIMIZED with caching
 */
function getBotStatistics() {
    // Check if we have cached stats (valid for 5 minutes)
    $cacheFile = '../data/admin_stats_cache.json';
    $cacheValid = false;

    if (file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        if ($cacheData && isset($cacheData['timestamp']) && (time() - $cacheData['timestamp']) < 300) {
            $cacheValid = true;
            return $cacheData['stats'];
        }
    }

    $stats = [
        'total_users' => 0,
        'active_users_7d' => 0,
        'active_users_30d' => 0,
        'total_referrals' => 0,
        'new_users_today' => 0,
        'banned_users' => 0
    ];

    if (STORAGE_MODE === 'json') {
        // Optimized JSON processing - read once, calculate efficiently
        $usersData = readJsonFile(USERS_FILE);
        $today = date('Y-m-d');
        $week_ago = date('Y-m-d', strtotime('-7 days'));
        $month_ago = date('Y-m-d', strtotime('-30 days'));

        $stats['total_users'] = count($usersData);

        foreach ($usersData as $userData) {
            if ($userData['banned'] ?? false) {
                $stats['banned_users']++;
            }

            $createdDate = date('Y-m-d', strtotime($userData['created_at'] ?? ''));
            if ($createdDate === $today) {
                $stats['new_users_today']++;
            }

            $lastActivity = date('Y-m-d', strtotime($userData['last_activity'] ?? $userData['created_at'] ?? ''));
            if ($lastActivity >= $week_ago) {
                $stats['active_users_7d']++;
            }
            if ($lastActivity >= $month_ago) {
                $stats['active_users_30d']++;
            }

            $stats['total_referrals'] += count($userData['promotion_report'] ?? []);
        }
    } else {
        try {
            $pdo = getDB();
            $stmt = $pdo->query("
                SELECT
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN banned = TRUE THEN 1 END) as banned_users,
                    COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as new_users_today,
                    COUNT(CASE WHEN last_activity >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d,
                    COUNT(CASE WHEN last_activity >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_users_30d
                FROM users
            ");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) {
                $stats = array_merge($stats, $result);
            }

            // Get total referrals
            $stmt = $pdo->query("SELECT COUNT(*) as total_referrals FROM promotion_reports");
            $referrals = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($referrals) {
                $stats['total_referrals'] = $referrals['total_referrals'];
            }
        } catch (Exception $e) {
            error_log("Error fetching bot statistics: " . $e->getMessage());
        }
    }

    // Cache the results
    $cacheData = [
        'timestamp' => time(),
        'stats' => $stats
    ];
    file_put_contents($cacheFile, json_encode($cacheData));

    return $stats;
}

/**
 * Get leaderboard data
 */
function getLeaderboards() {
    $leaderboards = [
        'top_referrers' => [],
        'top_withdrawers' => [],
        'top_balances' => []
    ];
    
    if (STORAGE_MODE === 'json') {
        $usersData = readJsonFile(USERS_FILE);
        $users = [];
        
        foreach ($usersData as $userId => $userData) {
            $users[] = [
                'user_id' => $userId,
                'first_name' => $userData['first_name'] ?? '',
                'username' => $userData['username'] ?? '',
                'total_referrals' => count($userData['promotion_report'] ?? []),
                'total_withdrawals' => $userData['successful_withdraw'] ?? 0,
                'balance' => $userData['balance'] ?? 0
            ];
        }
        
        // Sort and get top 10 for each category
        usort($users, function($a, $b) { return $b['total_referrals'] - $a['total_referrals']; });
        $leaderboards['top_referrers'] = array_slice($users, 0, 10);
        
        usort($users, function($a, $b) { return $b['total_withdrawals'] - $a['total_withdrawals']; });
        $leaderboards['top_withdrawers'] = array_slice($users, 0, 10);
        
        usort($users, function($a, $b) { return $b['balance'] - $a['balance']; });
        $leaderboards['top_balances'] = array_slice($users, 0, 10);
        
    } else {
        try {
            $pdo = getDB();
            
            // Top referrers
            $stmt = $pdo->query("
                SELECT u.user_id, u.first_name, u.username, COUNT(pr.id) as total_referrals
                FROM users u
                LEFT JOIN promotion_reports pr ON u.user_id = pr.referrer_id
                GROUP BY u.user_id
                ORDER BY total_referrals DESC
                LIMIT 10
            ");
            $leaderboards['top_referrers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Top withdrawers
            $stmt = $pdo->query("
                SELECT user_id, first_name, username, successful_withdraw as total_withdrawals
                FROM users
                ORDER BY successful_withdraw DESC
                LIMIT 10
            ");
            $leaderboards['top_withdrawers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Top balances
            $stmt = $pdo->query("
                SELECT user_id, first_name, username, balance
                FROM users
                ORDER BY balance DESC
                LIMIT 10
            ");
            $leaderboards['top_balances'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error fetching leaderboards: " . $e->getMessage());
        }
    }
    
    return $leaderboards;
}

/**
 * Export data to CSV
 */
function exportToCSV($data, $filename) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    if (!empty($data)) {
        // Write headers
        fputcsv($output, array_keys($data[0]));
        
        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
    exit;
}
?>
