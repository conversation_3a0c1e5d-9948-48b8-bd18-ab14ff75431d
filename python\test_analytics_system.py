"""
Test script for the Analytics System
Verifies all analytics functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    BOT_USERNAME = "testbot"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [123456789]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date
})
sys.modules['utils.helpers'] = helpers_module

# Mock database
class MockCollection:
    def __init__(self, collection_name):
        self.collection_name = collection_name
        self.data = []
    
    async def count_documents(self, query):
        # Return mock counts based on collection
        if self.collection_name == 'users':
            if query == {}:
                return 1000  # Total users
            elif query.get('banned') == {'$ne': True}:
                return 950  # Active users
            elif query.get('banned') == True:
                return 50  # Banned users
            elif 'created_at' in query:
                return 25  # Users in time range
            elif query.get('balance', {}).get('$gt') == 0:
                return 750  # Users with balance
        elif self.collection_name == 'withdrawals':
            if query == {}:
                return 200  # Total withdrawals
            elif query.get('status') == 'Pending':
                return 15  # Pending
            elif query.get('status') == 'Passed':
                return 150  # Approved
            elif query.get('status') == 'Failed':
                return 35  # Rejected
        elif self.collection_name == 'tasks':
            if query == {}:
                return 50  # Total tasks
            elif query.get('active') == True:
                return 30  # Active tasks
        elif self.collection_name == 'task_submissions':
            if query == {}:
                return 500  # Total submissions
            elif query.get('status') == 'Pending':
                return 25  # Pending
            elif query.get('status') == 'Approved':
                return 400  # Approved
            elif query.get('status') == 'Rejected':
                return 75  # Rejected
        elif self.collection_name == 'custom_referrals':
            if query == {}:
                return 20  # Total custom referrals
            elif query.get('active') == True:
                return 18  # Active custom referrals
        elif self.collection_name == 'force_channels':
            return 5  # Force channels
        elif self.collection_name == 'gift_codes':
            if query == {}:
                return 30  # Total gift codes
            else:
                return 25  # Active gift codes
        elif self.collection_name == 'admin_logs':
            if query == {}:
                return 100  # Total admin actions
            else:
                return 5  # Today's actions
        
        return 0

    async def find_one(self, query):
        # Mock find_one for user lookups
        if self.collection_name == 'users':
            user_id = query.get('user_id')
            if user_id == 12345:
                return {
                    'user_id': 12345,
                    'first_name': 'Top User',
                    'balance': 1000.0,
                    'submission_count': 25
                }
        return None

    def find(self, query=None):
        class MockCursor:
            def __init__(self, collection_name):
                self.collection_name = collection_name
            
            def sort(self, field, direction):
                return self
            
            def limit(self, count):
                return self
            
            async def to_list(self, length=None):
                if self.collection_name == 'users':
                    return [
                        {
                            'user_id': 12345,
                            'first_name': 'Top User',
                            'balance': 1000.0,
                            'promotion_report': [1, 2, 3, 4, 5],
                            'referral_count': 5
                        },
                        {
                            'user_id': 67890,
                            'first_name': 'Second User',
                            'balance': 750.0,
                            'promotion_report': [1, 2, 3],
                            'referral_count': 3
                        }
                    ]
                return []
        
        return MockCursor(self.collection_name)
    
    def aggregate(self, pipeline):
        class MockCursor:
            def __init__(self, collection_name, pipeline):
                self.collection_name = collection_name
                self.pipeline = pipeline
            
            async def to_list(self, length=None):
                # Mock aggregation results
                if self.collection_name == 'users':
                    if any('balance' in str(stage) for stage in self.pipeline):
                        return [{'total_balance': 50000.0}]
                    elif any('promotion_report' in str(stage) for stage in self.pipeline):
                        return [{'total_referrals': 1500}]
                    elif any('referral_count' in str(stage) for stage in self.pipeline):
                        return [
                            {'user_id': 12345, 'first_name': 'Top User', 'referral_count': 5},
                            {'user_id': 67890, 'first_name': 'Second User', 'referral_count': 3}
                        ]
                elif self.collection_name == 'withdrawals':
                    if any('Passed' in str(stage) for stage in self.pipeline):
                        return [{'total_amount': 25000.0}]
                    elif any('Pending' in str(stage) for stage in self.pipeline):
                        return [{'total_amount': 2500.0}]
                elif self.collection_name == 'custom_referrals':
                    if any('clicks' in str(stage) for stage in self.pipeline):
                        return [{'total_clicks': 500}]
                    elif any('referrals' in str(stage) for stage in self.pipeline):
                        return [{'total_conversions': 100}]
                elif self.collection_name == 'task_submissions':
                    return [{'_id': 12345, 'submission_count': 25}]

                return []
        
        return MockCursor(self.collection_name, pipeline)

async def mock_get_collection(name):
    return MockCollection(name)

# Mock database module
database_module = type('MockDatabase', (), {
    'get_collection': mock_get_collection,
    'COLLECTIONS': {
        'users': 'users',
        'withdrawals': 'withdrawals',
        'tasks': 'tasks',
        'task_submissions': 'task_submissions',
        'custom_referrals': 'custom_referrals',
        'force_channels': 'force_channels',
        'gift_codes': 'gift_codes',
        'admin_logs': 'admin_logs'
    }
})
sys.modules['config.database'] = database_module

from services.analytics_service import AnalyticsService
from models.analytics import AnalyticsModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnalyticsSystemTester:
    """Test the analytics system"""
    
    def __init__(self):
        self.analytics_service = AnalyticsService()
    
    async def run_all_tests(self):
        """Run all analytics system tests"""
        try:
            logger.info("Starting Analytics System Tests...")
            
            # Run tests
            await self.test_user_statistics()
            await self.test_financial_statistics()
            await self.test_task_statistics()
            await self.test_referral_statistics()
            await self.test_system_statistics()
            await self.test_comprehensive_statistics()
            await self.test_daily_statistics()
            await self.test_top_performers()
            await self.test_growth_metrics()
            await self.test_message_formatting()
            
            logger.info("All Analytics System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
    
    async def test_user_statistics(self):
        """Test user statistics"""
        logger.info("Testing user statistics...")
        
        stats = await self.analytics_service.get_user_statistics()
        
        assert stats['total_users'] == 1000, "Should have correct total users"
        assert stats['active_users'] == 950, "Should have correct active users"
        assert stats['banned_users'] == 50, "Should have correct banned users"
        assert stats['users_with_balance'] == 750, "Should have correct users with balance"
        assert len(stats['top_users']) > 0, "Should have top users"
        
        logger.info("✅ User statistics test passed")
    
    async def test_financial_statistics(self):
        """Test financial statistics"""
        logger.info("Testing financial statistics...")
        
        stats = await self.analytics_service.get_financial_statistics()
        
        assert stats['total_balance'] == 50000.0, "Should have correct total balance"
        assert stats['total_withdrawals'] == 200, "Should have correct total withdrawals"
        assert stats['pending_withdrawals'] == 15, "Should have correct pending withdrawals"
        assert stats['approved_withdrawals'] == 150, "Should have correct approved withdrawals"
        assert stats['total_withdrawn'] == 25000.0, "Should have correct total withdrawn"
        
        logger.info("✅ Financial statistics test passed")
    
    async def test_task_statistics(self):
        """Test task statistics"""
        logger.info("Testing task statistics...")
        
        stats = await self.analytics_service.get_task_statistics()
        
        assert stats['total_tasks'] == 50, "Should have correct total tasks"
        assert stats['active_tasks'] == 30, "Should have correct active tasks"
        assert stats['total_submissions'] == 500, "Should have correct total submissions"
        assert stats['pending_submissions'] == 25, "Should have correct pending submissions"
        assert stats['approved_submissions'] == 400, "Should have correct approved submissions"
        
        logger.info("✅ Task statistics test passed")
    
    async def test_referral_statistics(self):
        """Test referral statistics"""
        logger.info("Testing referral statistics...")
        
        stats = await self.analytics_service.get_referral_statistics()
        
        assert stats['total_referrals'] == 1500, "Should have correct total referrals"
        assert stats['total_custom_referrals'] == 20, "Should have correct custom referrals"
        assert stats['total_custom_clicks'] == 500, "Should have correct custom clicks"
        assert stats['total_custom_conversions'] == 100, "Should have correct custom conversions"
        
        logger.info("✅ Referral statistics test passed")
    
    async def test_system_statistics(self):
        """Test system statistics"""
        logger.info("Testing system statistics...")
        
        stats = await self.analytics_service.get_system_statistics()
        
        assert stats['total_force_channels'] == 5, "Should have correct force channels"
        assert stats['total_gift_codes'] == 30, "Should have correct gift codes"
        assert stats['active_gift_codes'] == 25, "Should have correct active gift codes"
        assert stats['total_admin_actions'] == 100, "Should have correct admin actions"
        
        logger.info("✅ System statistics test passed")
    
    async def test_comprehensive_statistics(self):
        """Test comprehensive statistics"""
        logger.info("Testing comprehensive statistics...")
        
        stats = await self.analytics_service.get_comprehensive_statistics()
        
        # Should contain all categories
        assert 'total_users' in stats, "Should contain user stats"
        assert 'total_balance' in stats, "Should contain financial stats"
        assert 'total_tasks' in stats, "Should contain task stats"
        assert 'total_referrals' in stats, "Should contain referral stats"
        assert 'total_force_channels' in stats, "Should contain system stats"
        
        logger.info("✅ Comprehensive statistics test passed")
    
    async def test_daily_statistics(self):
        """Test daily statistics"""
        logger.info("Testing daily statistics...")
        
        daily_stats = await self.analytics_service.get_daily_statistics(7)
        
        assert len(daily_stats) == 7, "Should return 7 days of data"
        
        for day_stat in daily_stats:
            assert 'date' in day_stat, "Should have date"
            assert 'day_name' in day_stat, "Should have day name"
            assert 'new_users' in day_stat, "Should have new users count"
            
        logger.info("✅ Daily statistics test passed")
    
    async def test_top_performers(self):
        """Test top performers"""
        logger.info("Testing top performers...")
        
        performers = await self.analytics_service.get_top_performers()
        
        assert 'top_balance' in performers, "Should have top balance users"
        assert 'top_referrals' in performers, "Should have top referral users"
        assert 'top_submitters' in performers, "Should have top submitters"
        
        logger.info("✅ Top performers test passed")
    
    async def test_growth_metrics(self):
        """Test growth metrics"""
        logger.info("Testing growth metrics...")
        
        growth = await self.analytics_service.get_growth_metrics()
        
        assert 'users_this_month' in growth, "Should have this month's users"
        assert 'users_last_month' in growth, "Should have last month's users"
        assert 'monthly_growth_rate' in growth, "Should have monthly growth rate"
        assert 'weekly_growth_rate' in growth, "Should have weekly growth rate"
        
        logger.info("✅ Growth metrics test passed")
    
    async def test_message_formatting(self):
        """Test message formatting"""
        logger.info("Testing message formatting...")
        
        # Test comprehensive statistics message
        stats = await self.analytics_service.get_comprehensive_statistics()
        message = AnalyticsModel.format_comprehensive_statistics_message(stats)
        assert "Bot Statistics Dashboard" in message, "Should contain title"
        assert "User Statistics:" in message, "Should contain user section"
        assert "Financial Statistics:" in message, "Should contain financial section"
        
        # Test daily statistics message
        daily_stats = await self.analytics_service.get_daily_statistics(3)
        daily_message = AnalyticsModel.format_daily_statistics_message(daily_stats)
        assert "Daily Statistics" in daily_message, "Should contain daily title"
        
        # Test top performers message
        performers = await self.analytics_service.get_top_performers()
        performers_message = AnalyticsModel.format_top_performers_message(performers)
        assert "Top Performers" in performers_message, "Should contain performers title"
        
        # Test growth metrics message
        growth = await self.analytics_service.get_growth_metrics()
        growth_message = AnalyticsModel.format_growth_metrics_message(growth)
        assert "Growth Metrics" in growth_message, "Should contain growth title"
        
        # Test keyboard creation
        keyboard = AnalyticsModel.create_analytics_dashboard_keyboard()
        assert keyboard is not None, "Should create dashboard keyboard"
        
        nav_keyboard = AnalyticsModel.create_analytics_navigation_keyboard()
        assert nav_keyboard is not None, "Should create navigation keyboard"
        
        logger.info("✅ Message formatting test passed")

def main():
    """Main test function"""
    async def run_tests():
        tester = AnalyticsSystemTester()
        
        try:
            success = await tester.run_all_tests()
            
            if success:
                print("\n🎉 All Analytics System tests PASSED!")
                print("The analytics system is ready for production use.")
            else:
                print("\n❌ Some tests FAILED!")
                print("Please check the logs for details.")
                sys.exit(1)
                
        except Exception as e:
            print(f"\n💥 Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(run_tests())

if __name__ == "__main__":
    main()
