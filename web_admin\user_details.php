<?php
/**
 * User Details Modal Content
 * Displays comprehensive user information
 */

require_once 'config.php';
requireAuth();

$userId = $_GET['user_id'] ?? null;

if (!$userId) {
    echo '<div class="alert alert-danger">User ID not provided</div>';
    exit;
}

// Get user data with referral chain
$user = null;
$referralChain = null;

if (STORAGE_MODE === 'json') {
    $usersData = readJsonFile(USERS_FILE);
    if (isset($usersData[$userId])) {
        $user = formatUserData($userId, $usersData[$userId]);
        $user['referrer_info'] = getReferrerInfo($usersData[$userId]['referred_by'] ?? 'None');
        $referralChain = getReferralChain($userId);
    }
} else {
    try {
        $pdo = getDB();
        $stmt = $pdo->prepare("
            SELECT u.*,
                   ua.name, ua.ifsc, ua.email, ua.account_number, ua.mobile_number,
                   ref.first_name as referrer_name, ref.username as referrer_username
            FROM users u
            LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
            LEFT JOIN users ref ON u.referred_by = ref.user_id
            WHERE u.user_id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Add referrer info
            if ($user['referred_by'] !== 'None' && !empty($user['referrer_name'])) {
                $user['referrer_info'] = [
                    'user_id' => $user['referred_by'],
                    'first_name' => $user['referrer_name'],
                    'username' => $user['referrer_username'],
                    'referral_date' => $user['created_at']
                ];
            } else {
                $user['referrer_info'] = null;
            }

            // Get referrals
            $stmt = $pdo->prepare("
                SELECT pr.*, u.first_name, u.username, u.balance, u.banned
                FROM promotion_reports pr
                LEFT JOIN users u ON pr.referred_user_id = u.user_id
                WHERE pr.referrer_id = ?
                ORDER BY pr.created_at DESC
            ");
            $stmt->execute([$userId]);
            $user['referrals'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get withdrawal history
            $stmt = $pdo->prepare("SELECT * FROM withdrawal_reports WHERE user_id = ? ORDER BY created_at DESC");
            $stmt->execute([$userId]);
            $user['withdrawal_history'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get referral chain
            $referralChain = getReferralChain($userId);
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading user data: ' . htmlspecialchars($e->getMessage()) . '</div>';
        exit;
    }
}

if (!$user) {
    echo '<div class="alert alert-warning">User not found</div>';
    exit;
}
?>

<div class="row">
    <!-- User Basic Info -->
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Basic Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>User ID:</strong></td>
                        <td><?php echo htmlspecialchars($user['user_id']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td><?php echo htmlspecialchars($user['first_name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>@<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <?php if ($user['banned'] ?? false): ?>
                                <span class="badge bg-danger">Banned</span>
                            <?php else: ?>
                                <span class="badge bg-success">Active</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Referred By:</strong></td>
                        <td>
                            <?php if ($user['referrer_info']): ?>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-2" style="width: 25px; height: 25px; font-size: 10px;">
                                        <i class="fas fa-user-friends"></i>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($user['referrer_info']['first_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            @<?php echo htmlspecialchars($user['referrer_info']['username'] ?: 'N/A'); ?>
                                            (ID: <?php echo $user['referrer_info']['user_id']; ?>)
                                        </small>
                                    </div>
                                </div>
                            <?php else: ?>
                                <span class="badge bg-secondary">Direct Join</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Join Date:</strong></td>
                        <td><?php echo date('M d, Y H:i', strtotime($user['created_at'] ?? 'now')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Last Activity:</strong></td>
                        <td><?php echo date('M d, Y H:i', strtotime($user['last_activity'] ?? 'now')); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Financial Info -->
    <div class="col-md-6">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-wallet me-2"></i>Financial Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Current Balance:</strong></td>
                        <td><span class="text-success">₹<?php echo number_format($user['balance'] ?? 0, 2); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Total Withdrawn:</strong></td>
                        <td><span class="text-primary">₹<?php echo number_format($user['successful_withdraw'] ?? 0, 2); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Pending Withdrawal:</strong></td>
                        <td><span class="text-warning">₹<?php echo number_format($user['withdraw_under_review'] ?? 0, 2); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Joining Bonus:</strong></td>
                        <td>₹<?php echo number_format($user['joining_bonus_got'] ?? 0, 2); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Referral Earnings:</strong></td>
                        <td><span class="text-success">₹<?php echo number_format($user['referral_earnings'] ?? 0, 2); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Total Referrals:</strong></td>
                        <td><span class="badge bg-info"><?php echo count($user['referrals'] ?? []); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Gift Claimed:</strong></td>
                        <td>
                            <?php if ($user['gift_claimed'] ?? false): ?>
                                <span class="badge bg-success">Yes</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">No</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Account Information -->
<?php if (isset($user['name']) || isset($user['account_number'])): ?>
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>Account Information</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Account Holder:</strong></td>
                        <td><?php echo htmlspecialchars($user['name'] ?? 'Not provided'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Account Number:</strong></td>
                        <td><?php echo htmlspecialchars($user['account_number'] ?? 'Not provided'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>IFSC Code:</strong></td>
                        <td><?php echo htmlspecialchars($user['ifsc'] ?? 'Not provided'); ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?php echo htmlspecialchars($user['email'] ?? 'Not provided'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Mobile Number:</strong></td>
                        <td><?php echo htmlspecialchars($user['mobile_number'] ?? 'Not provided'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Referrals -->
<?php if (!empty($user['referrals'])): ?>
<div class="card mb-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0"><i class="fas fa-users me-2"></i>Referrals (<?php echo count($user['referrals']); ?>)</h6>
        <div>
            <span class="badge bg-success">₹<?php echo number_format($user['referral_earnings'] ?? 0, 2); ?> Total Earned</span>
            <button class="btn btn-sm btn-outline-primary ms-2" onclick="viewReferralChain(<?php echo $user['user_id']; ?>)">
                <i class="fas fa-sitemap me-1"></i>View Chain
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Referred User</th>
                        <th>Username</th>
                        <th>Current Balance</th>
                        <th>Commission Earned</th>
                        <th>Status</th>
                        <th>Referral Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($user['referrals'] as $referral): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2" style="width: 25px; height: 25px; font-size: 10px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <strong><?php echo htmlspecialchars($referral['referred_user_name'] ?? $referral['first_name'] ?? 'Unknown'); ?></strong>
                            </div>
                        </td>
                        <td>@<?php echo htmlspecialchars($referral['username'] ?? 'N/A'); ?></td>
                        <td>
                            <?php if (isset($referral['balance'])): ?>
                                <span class="text-success">₹<?php echo number_format($referral['balance'], 2); ?></span>
                            <?php else: ?>
                                <span class="text-muted">N/A</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-success">₹<?php echo number_format($referral['amount_got'] ?? 0, 2); ?></span>
                        </td>
                        <td>
                            <?php if (isset($referral['banned']) && $referral['banned']): ?>
                                <span class="badge bg-danger">Banned</span>
                            <?php else: ?>
                                <span class="badge bg-success">Active</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small><?php echo isset($referral['created_at']) ? date('M d, Y', strtotime($referral['created_at'])) : (isset($referral['date']) ? date('M d, Y', strtotime($referral['date'])) : 'N/A'); ?></small>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetails(<?php echo $referral['referred_user_id'] ?? 'null'; ?>)" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php else: ?>
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-users me-2"></i>Referrals</h6>
    </div>
    <div class="card-body text-center">
        <div class="text-muted">
            <i class="fas fa-user-plus fa-3x mb-3"></i>
            <p>This user hasn't referred anyone yet.</p>
            <small>When they refer users, the details will appear here.</small>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Withdrawal History -->
<?php if (!empty($user['withdrawal_history'])): ?>
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-history me-2"></i>Withdrawal History (<?php echo count($user['withdrawal_history']); ?>)</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($user['withdrawal_history'] as $withdrawal): ?>
                    <tr>
                        <td>₹<?php echo number_format($withdrawal['amount'] ?? 0, 2); ?></td>
                        <td>
                            <?php
                            $status = $withdrawal['status'] ?? 'Unknown';
                            $badgeClass = 'bg-secondary';
                            switch ($status) {
                                case 'Passed':
                                    $badgeClass = 'bg-success';
                                    break;
                                case 'Failed':
                                    $badgeClass = 'bg-danger';
                                    break;
                                case 'Under review':
                                    $badgeClass = 'bg-warning';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($status); ?></span>
                        </td>
                        <td><?php echo isset($withdrawal['date']) ? date('M d, Y H:i', strtotime($withdrawal['date'])) : 'N/A'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="d-flex justify-content-end gap-2">
    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editUserBalance(<?php echo $user['user_id']; ?>)">
        <i class="fas fa-edit me-1"></i>Edit Balance
    </button>
    <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleUserStatus(<?php echo $user['user_id']; ?>, <?php echo $user['banned'] ? 'false' : 'true'; ?>)">
        <i class="fas fa-<?php echo $user['banned'] ? 'unlock' : 'ban'; ?> me-1"></i>
        <?php echo $user['banned'] ? 'Unban' : 'Ban'; ?> User
    </button>
    <button type="button" class="btn btn-outline-info btn-sm" onclick="viewUserActivity(<?php echo $user['user_id']; ?>)">
        <i class="fas fa-chart-line me-1"></i>View Activity
    </button>
</div>

<script>
function editUserBalance(userId) {
    const newBalance = prompt('Enter new balance for user:');
    if (newBalance !== null && !isNaN(newBalance)) {
        // Implement balance update functionality
        alert('Balance update functionality would be implemented here');
    }
}

function toggleUserStatus(userId, ban) {
    const action = ban ? 'ban' : 'unban';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        // Implement ban/unban functionality
        alert(`User ${action} functionality would be implemented here`);
    }
}

function viewUserActivity(userId) {
    // Implement user activity view
    alert('User activity view would be implemented here');
}

function viewReferralChain(userId) {
    // Use parent window's function if available
    if (window.parent && window.parent.viewReferralChain) {
        window.parent.viewReferralChain(userId);
    } else {
        alert('Referral chain functionality not available');
    }
}

function viewUserDetails(userId) {
    // Use parent window's function if available
    if (window.parent && window.parent.viewUserDetails) {
        window.parent.viewUserDetails(userId);
    } else {
        alert('User details functionality not available');
    }
}
</script>
