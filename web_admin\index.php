<?php
/**
 * Comprehensive Single-Page Web Admin Panel
 * For Telegram Referral Bot Management
 */

require_once 'config.php';

// Handle actions
$action = $_GET['action'] ?? $_POST['action'] ?? 'dashboard';

// Handle login with improved error handling
if ($action === 'login') {
    if ($_POST['login_code'] ?? false) {
        try {
            if (authenticate($_POST['login_code'])) {
                // Successful login - redirect with success
                header('Location: index.php?login=success');
                exit;
            } else {
                $error = 'Invalid login code. Please try again.';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'Login system error. Please try again.';
        }
    }

    // Show login form
    include 'login.php';
    exit;
}

// Handle logout
if ($action === 'logout') {
    logout();
    header('Location: index.php?action=login&logout=success');
    exit;
}

// Handle AJAX requests for better performance
if ($action === 'ajax') {
    requireAuth();
    header('Content-Type: application/json');

    $ajaxAction = $_GET['ajax_action'] ?? '';

    switch ($ajaxAction) {
        case 'get_users':
            $page = max(1, intval($_GET['page'] ?? 1));
            $search = $_GET['search'] ?? '';
            $filter = $_GET['filter'] ?? 'all';
            $referralFilter = $_GET['referral_filter'] ?? 'all';

            $result = getUsersPaginated($page, WEB_ADMIN_USERS_PER_PAGE, $search, $filter, $referralFilter);
            echo json_encode($result);
            exit;

        case 'get_stats':
            $stats = [
                'financial' => getFinancialOverview(),
                'bot_stats' => getBotStatistics()
            ];
            echo json_encode($stats);
            exit;
    }

    echo json_encode(['error' => 'Invalid AJAX action']);
    exit;
}

// Handle export with pagination support
if ($action === 'export') {
    requireAuth();
    $type = $_GET['type'] ?? 'users';

    try {
        switch ($type) {
            case 'users':
                // Export with pagination to avoid memory issues
                $allUsers = [];
                $page = 1;
                do {
                    $result = getUsersPaginated($page, 100, '', 'all', 'all');
                    $allUsers = array_merge($allUsers, $result['users']);
                    $page++;
                } while ($page <= $result['totalPages']);

                exportToCSV($allUsers, 'users_export_' . date('Y-m-d') . '.csv');
                break;

            case 'financial':
                $financial = getFinancialOverview();
                exportToCSV([$financial], 'financial_overview_' . date('Y-m-d') . '.csv');
                break;
        }
    } catch (Exception $e) {
        error_log("Export error: " . $e->getMessage());
        header('Location: index.php?error=export_failed');
        exit;
    }
}

// Require authentication for all other actions
requireAuth();

// Initialize variables with error handling
try {
    // Get basic data for dashboard (optimized)
    $financial = getFinancialOverview();
    $statistics = getBotStatistics();
    $leaderboards = getLeaderboards();

    // Pagination settings
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = WEB_ADMIN_USERS_PER_PAGE;

    // Search and filter parameters
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? 'all';
    $referralFilter = $_GET['referral_filter'] ?? 'all';

    // Get paginated users (only load when needed)
    $usersPaginated = null;
    if (isset($_GET['tab']) && $_GET['tab'] === 'users') {
        $usersPaginated = getUsersPaginated($page, $perPage, $search, $filter, $referralFilter);
    }

} catch (Exception $e) {
    error_log("Dashboard initialization error: " . $e->getMessage());
    $error = "Dashboard loading error. Please refresh the page.";

    // Set default values to prevent errors
    $financial = ['total_withdrawals' => 0, 'pending_withdrawals' => 0, 'total_user_balances' => 0, 'total_users' => 0];
    $statistics = ['total_users' => 0, 'active_users_7d' => 0, 'active_users_30d' => 0, 'total_referrals' => 0, 'new_users_today' => 0, 'banned_users' => 0];
    $leaderboards = ['top_referrers' => [], 'top_withdrawers' => [], 'top_balances' => []];
    $usersPaginated = ['users' => [], 'total' => 0, 'page' => 1, 'perPage' => $perPage, 'totalPages' => 0];
}

// Filtering and pagination is now handled in getUsersPaginated() function
// This section is removed to improve performance
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 0;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 30px;
            border-radius: 20px 20px 0 0;
        }
        
        .nav-tabs {
            border-bottom: none;
            background: #f8f9fa;
            padding: 10px 20px 0;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
            color: #666;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            background: white;
            color: var(--primary-color);
            border-bottom: 3px solid var(--primary-color);
        }
        
        .tab-content {
            padding: 30px;
            background: white;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge-status {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .search-box {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .leaderboard-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }
        
        .rank-badge {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .avatar-circle {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .header {
                padding: 15px 20px;
            }
            
            .tab-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="mb-0"><i class="fas fa-robot me-3"></i>Telegram Bot Admin Panel</h1>
                        <p class="mb-0 opacity-75">Comprehensive Management Dashboard - Optimized for <?php echo number_format($statistics['total_users']); ?> Users</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex justify-content-end align-items-center">
                            <span class="me-3">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo date('M d, Y H:i'); ?>
                            </span>
                            <span class="me-3 badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Online
                            </span>
                            <a href="performance_monitor.php" class="btn btn-outline-light btn-sm me-2" title="Performance Monitor">
                                <i class="fas fa-tachometer-alt me-1"></i>Performance
                            </a>
                            <a href="clear_cache.php" class="btn btn-outline-light btn-sm me-2" title="Cache Management">
                                <i class="fas fa-broom me-1"></i>Cache
                            </a>
                            <a href="?action=logout" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (isset($_GET['login']) && $_GET['login'] === 'success'): ?>
                <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    Successfully logged in! Welcome to the admin panel.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php
                    switch($_GET['error']) {
                        case 'export_failed':
                            echo 'Export failed. Please try again or contact support.';
                            break;
                        default:
                            echo 'An error occurred. Please refresh the page.';
                    }
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="adminTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>User Management
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                        <i class="fas fa-chart-pie me-2"></i>Financial Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="leaderboards-tab" data-bs-toggle="tab" data-bs-target="#leaderboards" type="button" role="tab">
                        <i class="fas fa-trophy me-2"></i>Leaderboards
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab">
                        <i class="fas fa-chart-bar me-2"></i>Bot Statistics
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="adminTabsContent">
                <!-- Overview Tab -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel">
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon mx-auto mb-3" style="background: var(--primary-color);">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h3 class="mb-1"><?php echo number_format($statistics['total_users']); ?></h3>
                                    <p class="text-muted mb-0">Total Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon mx-auto mb-3" style="background: var(--success-color);">
                                        <i class="fas fa-rupee-sign"></i>
                                    </div>
                                    <h3 class="mb-1">₹<?php echo number_format($financial['total_withdrawals'], 2); ?></h3>
                                    <p class="text-muted mb-0">Total Withdrawals</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon mx-auto mb-3" style="background: var(--warning-color);">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h3 class="mb-1">₹<?php echo number_format($financial['pending_withdrawals'], 2); ?></h3>
                                    <p class="text-muted mb-0">Pending Withdrawals</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon mx-auto mb-3" style="background: var(--info-color);">
                                        <i class="fas fa-wallet"></i>
                                    </div>
                                    <h3 class="mb-1">₹<?php echo number_format($financial['total_user_balances'], 2); ?></h3>
                                    <p class="text-muted mb-0">Total User Balances</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>User Growth</h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="userGrowthChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>User Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="userActivityChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Tab -->
                <div class="tab-pane fade" id="users" role="tabpanel">
                    <!-- Performance Notice -->
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Optimized for Large Datasets:</strong>
                        Showing <?php echo WEB_ADMIN_USERS_PER_PAGE; ?> users per page for optimal performance.
                        Use search and filters to find specific users quickly.
                    </div>

                    <!-- Search and Filter -->
                    <div class="search-box">
                        <form method="GET" class="row g-3" id="userSearchForm">
                            <input type="hidden" name="tab" value="users">
                            <div class="col-md-3">
                                <label class="form-label">Search Users</label>
                                <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by name, username, ID">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status Filter</label>
                                <select class="form-select" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Users</option>
                                    <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Users</option>
                                    <option value="banned" <?php echo $filter === 'banned' ? 'selected' : ''; ?>>Banned Users</option>
                                    <option value="high_balance" <?php echo $filter === 'high_balance' ? 'selected' : ''; ?>>High Balance (>₹100)</option>
                                    <option value="pending_withdrawal" <?php echo $filter === 'pending_withdrawal' ? 'selected' : ''; ?>>Pending Withdrawals</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Referral Filter</label>
                                <select class="form-select" name="referral_filter">
                                    <option value="all" <?php echo $referralFilter === 'all' ? 'selected' : ''; ?>>All Referral Types</option>
                                    <option value="has_referrer" <?php echo $referralFilter === 'has_referrer' ? 'selected' : ''; ?>>Has Referrer</option>
                                    <option value="no_referrer" <?php echo $referralFilter === 'no_referrer' ? 'selected' : ''; ?>>No Referrer (Direct)</option>
                                    <option value="has_referrals" <?php echo $referralFilter === 'has_referrals' ? 'selected' : ''; ?>>Has Made Referrals</option>
                                    <option value="no_referrals" <?php echo $referralFilter === 'no_referrals' ? 'selected' : ''; ?>>No Referrals Made</option>
                                    <option value="high_referrals" <?php echo $referralFilter === 'high_referrals' ? 'selected' : ''; ?>>High Referrers (5+)</option>
                                    <option value="top_earners" <?php echo $referralFilter === 'top_earners' ? 'selected' : ''; ?>>Top Earners (₹50+)</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary-custom btn-custom">
                                        <i class="fas fa-search me-1"></i>Search
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="?action=export&type=users" class="btn btn-success btn-custom" onclick="return confirm('Export may take a few minutes for large datasets. Continue?')">
                                        <i class="fas fa-download me-1"></i>Export All
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading users...</p>
                    </div>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User Info</th>
                                    <th>Referred By</th>
                                    <th>Balance</th>
                                    <th>Referrals Made</th>
                                    <th>Referral Earnings</th>
                                    <th>Withdrawals</th>
                                    <th>Status</th>
                                    <th>Join Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Load users only when on users tab
                                if (!$usersPaginated) {
                                    $usersPaginated = getUsersPaginated($page, $perPage, $search, $filter, $referralFilter);
                                }

                                if (empty($usersPaginated['users'])): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No users found</h5>
                                        <p class="text-muted">Try adjusting your search criteria or filters.</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($usersPaginated['users'] as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    @<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?>
                                                    <br>
                                                    ID: <?php echo $user['user_id']; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($user['referrer_info']): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                                    <i class="fas fa-user-friends"></i>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user['referrer_info']['first_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        @<?php echo htmlspecialchars($user['referrer_info']['username'] ?: 'N/A'); ?>
                                                        <br>
                                                        ID: <?php echo $user['referrer_info']['user_id']; ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Direct Join</span>
                                            <br>
                                            <small class="text-muted">No referrer</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong>₹<?php echo number_format($user['balance'], 2); ?></strong>
                                        <?php if ($user['withdraw_under_review'] > 0): ?>
                                            <br><small class="text-warning">₹<?php echo number_format($user['withdraw_under_review'], 2); ?> pending</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $user['total_referrals']; ?></span>
                                        <?php if ($user['total_referrals'] > 0): ?>
                                            <br><small class="text-success">Active referrer</small>
                                        <?php else: ?>
                                            <br><small class="text-muted">No referrals</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong class="text-success">₹<?php echo number_format($user['referral_earnings'] ?? 0, 2); ?></strong>
                                        <?php if (($user['referral_earnings'] ?? 0) > 0): ?>
                                            <br><small class="text-muted">From <?php echo $user['total_referrals']; ?> referrals</small>
                                        <?php else: ?>
                                            <br><small class="text-muted">No earnings</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong>₹<?php echo number_format($user['successful_withdraw'], 2); ?></strong>
                                        <?php if (count($user['withdrawal_history'] ?? []) > 0): ?>
                                            <br><small class="text-muted"><?php echo count($user['withdrawal_history']); ?> transactions</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['banned']): ?>
                                            <span class="badge badge-status bg-danger">Banned</span>
                                        <?php else: ?>
                                            <span class="badge badge-status bg-success">Active</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo date('M d, Y', strtotime($user['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewUserDetails(<?php echo $user['user_id']; ?>)" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="viewReferralChain(<?php echo $user['user_id']; ?>)" title="View Referral Chain">
                                                <i class="fas fa-sitemap"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="editUser(<?php echo $user['user_id']; ?>)" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination and Results Info -->
                    <?php if ($usersPaginated && $usersPaginated['total'] > 0): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Showing <?php echo (($usersPaginated['page'] - 1) * $usersPaginated['perPage']) + 1; ?>
                                to <?php echo min($usersPaginated['page'] * $usersPaginated['perPage'], $usersPaginated['total']); ?>
                                of <?php echo number_format($usersPaginated['total']); ?> users
                            </small>
                        </div>

                        <?php if ($usersPaginated['totalPages'] > 1): ?>
                        <nav aria-label="User pagination">
                            <ul class="pagination pagination-sm mb-0">
                                <!-- Previous button -->
                                <?php if ($usersPaginated['page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=users&page=<?php echo $usersPaginated['page'] - 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&referral_filter=<?php echo $referralFilter; ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <!-- Page numbers (show max 5 pages) -->
                                <?php
                                $startPage = max(1, $usersPaginated['page'] - 2);
                                $endPage = min($usersPaginated['totalPages'], $usersPaginated['page'] + 2);

                                for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <li class="page-item <?php echo $i === $usersPaginated['page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?tab=users&page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&referral_filter=<?php echo $referralFilter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <!-- Next button -->
                                <?php if ($usersPaginated['page'] < $usersPaginated['totalPages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?tab=users&page=<?php echo $usersPaginated['page'] + 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&referral_filter=<?php echo $referralFilter; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Financial Overview Tab -->
                <div class="tab-pane fade" id="financial" role="tabpanel">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Financial Overview</h5>
                                    <a href="?action=export&type=financial" class="btn btn-success btn-sm">
                                        <i class="fas fa-download me-1"></i>Export Financial Data
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <div class="text-center p-3 bg-light rounded">
                                                <h4 class="text-success">₹<?php echo number_format($financial['total_withdrawals'], 2); ?></h4>
                                                <p class="mb-0 text-muted">Total Withdrawals</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="text-center p-3 bg-light rounded">
                                                <h4 class="text-warning">₹<?php echo number_format($financial['pending_withdrawals'], 2); ?></h4>
                                                <p class="mb-0 text-muted">Pending Withdrawals</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="text-center p-3 bg-light rounded">
                                                <h4 class="text-info">₹<?php echo number_format($financial['total_user_balances'], 2); ?></h4>
                                                <p class="mb-0 text-muted">Total User Balances</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="text-center p-3 bg-light rounded">
                                                <h4 class="text-primary">₹<?php echo number_format($financial['commission_earned'], 2); ?></h4>
                                                <p class="mb-0 text-muted">Commission Earned</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <canvas id="financialChart"></canvas>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Financial Breakdown</h6>
                                            <div class="list-group">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    Total Revenue
                                                    <span class="badge bg-primary rounded-pill">₹<?php echo number_format($financial['total_withdrawals'] + $financial['pending_withdrawals'], 2); ?></span>
                                                </div>
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    Active Balances
                                                    <span class="badge bg-info rounded-pill">₹<?php echo number_format($financial['total_user_balances'], 2); ?></span>
                                                </div>
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    Total Users
                                                    <span class="badge bg-secondary rounded-pill"><?php echo number_format($financial['total_users']); ?></span>
                                                </div>
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    Avg Balance per User
                                                    <span class="badge bg-success rounded-pill">₹<?php echo $financial['total_users'] > 0 ? number_format($financial['total_user_balances'] / $financial['total_users'], 2) : '0.00'; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Leaderboards Tab -->
                <div class="tab-pane fade" id="leaderboards" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>Top Referrers</h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($leaderboards['top_referrers'] as $index => $user): ?>
                                    <div class="leaderboard-item">
                                        <div class="d-flex align-items-center">
                                            <div class="rank-badge me-3"><?php echo $index + 1; ?></div>
                                            <div class="flex-grow-1">
                                                <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?></small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-primary"><?php echo $user['total_referrals']; ?></span>
                                                <br>
                                                <small class="text-muted">referrals</small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Top Withdrawers</h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($leaderboards['top_withdrawers'] as $index => $user): ?>
                                    <div class="leaderboard-item">
                                        <div class="d-flex align-items-center">
                                            <div class="rank-badge me-3"><?php echo $index + 1; ?></div>
                                            <div class="flex-grow-1">
                                                <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?></small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-success">₹<?php echo number_format($user['total_withdrawals'], 2); ?></span>
                                                <br>
                                                <small class="text-muted">withdrawn</small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-wallet me-2"></i>Highest Balances</h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($leaderboards['top_balances'] as $index => $user): ?>
                                    <div class="leaderboard-item">
                                        <div class="d-flex align-items-center">
                                            <div class="rank-badge me-3"><?php echo $index + 1; ?></div>
                                            <div class="flex-grow-1">
                                                <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($user['username'] ?: 'N/A'); ?></small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-info">₹<?php echo number_format($user['balance'], 2); ?></span>
                                                <br>
                                                <small class="text-muted">balance</small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bot Statistics Tab -->
                <div class="tab-pane fade" id="statistics" role="tabpanel">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Bot Statistics Dashboard</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-4">
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-primary text-white rounded">
                                                <h4><?php echo number_format($statistics['total_users']); ?></h4>
                                                <p class="mb-0">Total Users</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-success text-white rounded">
                                                <h4><?php echo number_format($statistics['active_users_7d']); ?></h4>
                                                <p class="mb-0">Active (7d)</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-info text-white rounded">
                                                <h4><?php echo number_format($statistics['active_users_30d']); ?></h4>
                                                <p class="mb-0">Active (30d)</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-warning text-white rounded">
                                                <h4><?php echo number_format($statistics['total_referrals']); ?></h4>
                                                <p class="mb-0">Total Referrals</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-secondary text-white rounded">
                                                <h4><?php echo number_format($statistics['new_users_today']); ?></h4>
                                                <p class="mb-0">New Today</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <div class="text-center p-3 bg-danger text-white rounded">
                                                <h4><?php echo number_format($statistics['banned_users']); ?></h4>
                                                <p class="mb-0">Banned Users</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">User Activity Distribution</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="chart-container">
                                                        <canvas id="activityDistributionChart"></canvas>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">Conversion Metrics</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row text-center">
                                                        <div class="col-6 mb-3">
                                                            <h5 class="text-primary"><?php echo $statistics['total_users'] > 0 ? number_format(($statistics['total_referrals'] / $statistics['total_users']) * 100, 1) : '0'; ?>%</h5>
                                                            <small class="text-muted">Referral Rate</small>
                                                        </div>
                                                        <div class="col-6 mb-3">
                                                            <h5 class="text-success"><?php echo $statistics['total_users'] > 0 ? number_format(($statistics['active_users_7d'] / $statistics['total_users']) * 100, 1) : '0'; ?>%</h5>
                                                            <small class="text-muted">7-Day Activity</small>
                                                        </div>
                                                        <div class="col-6 mb-3">
                                                            <h5 class="text-info"><?php echo $statistics['total_users'] > 0 ? number_format(($statistics['active_users_30d'] / $statistics['total_users']) * 100, 1) : '0'; ?>%</h5>
                                                            <small class="text-muted">30-Day Activity</small>
                                                        </div>
                                                        <div class="col-6 mb-3">
                                                            <h5 class="text-warning"><?php echo $statistics['total_users'] > 0 ? number_format(($statistics['banned_users'] / $statistics['total_users']) * 100, 1) : '0'; ?>%</h5>
                                                            <small class="text-muted">Ban Rate</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userDetailsContent">
                    <!-- User details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Referral Chain Modal -->
    <div class="modal fade" id="referralChainModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-sitemap me-2"></i>Referral Chain Visualization</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="referralChainContent">
                    <!-- Referral chain will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Performance optimizations and user experience improvements
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.querySelector('.btn-close')) {
                        alert.querySelector('.btn-close').click();
                    }
                }, 5000);
            });

            // Add loading states to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
                    }
                });
            });

            // Tab switching with URL update
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    const tabId = e.target.getAttribute('data-bs-target').replace('#', '');
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabId);
                    window.history.replaceState({}, '', url);
                });
            });

            // Activate tab based on URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            const activeTab = urlParams.get('tab');
            if (activeTab) {
                const tabButton = document.querySelector(`[data-bs-target="#${activeTab}"]`);
                if (tabButton) {
                    new bootstrap.Tab(tabButton).show();
                }
            }

        // Initialize charts
            // User Growth Chart
            const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
            new Chart(userGrowthCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'New Users',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // User Activity Chart
            const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
            new Chart(userActivityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active (7d)', 'Active (30d)', 'Inactive'],
                    datasets: [{
                        data: [<?php echo $statistics['active_users_7d']; ?>, <?php echo $statistics['active_users_30d'] - $statistics['active_users_7d']; ?>, <?php echo $statistics['total_users'] - $statistics['active_users_30d']; ?>],
                        backgroundColor: ['#28a745', '#17a2b8', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Financial Chart
            const financialCtx = document.getElementById('financialChart').getContext('2d');
            new Chart(financialCtx, {
                type: 'pie',
                data: {
                    labels: ['Completed Withdrawals', 'Pending Withdrawals', 'User Balances'],
                    datasets: [{
                        data: [<?php echo $financial['total_withdrawals']; ?>, <?php echo $financial['pending_withdrawals']; ?>, <?php echo $financial['total_user_balances']; ?>],
                        backgroundColor: ['#28a745', '#ffc107', '#17a2b8']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Activity Distribution Chart
            const activityDistributionCtx = document.getElementById('activityDistributionChart').getContext('2d');
            new Chart(activityDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['Total', 'Active 7d', 'Active 30d', 'New Today', 'Banned'],
                    datasets: [{
                        label: 'Users',
                        data: [<?php echo $statistics['total_users']; ?>, <?php echo $statistics['active_users_7d']; ?>, <?php echo $statistics['active_users_30d']; ?>, <?php echo $statistics['new_users_today']; ?>, <?php echo $statistics['banned_users']; ?>],
                        backgroundColor: ['#667eea', '#28a745', '#17a2b8', '#ffc107', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });

        // User management functions
        function viewUserDetails(userId) {
            // Load user details via AJAX
            fetch(`user_details.php?user_id=${userId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('userDetailsContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
                })
                .catch(error => {
                    console.error('Error loading user details:', error);
                    alert('Error loading user details');
                });
        }

        function viewReferralChain(userId) {
            // Load referral chain via AJAX
            fetch(`referral_chain.php?user_id=${userId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('referralChainContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('referralChainModal')).show();
                })
                .catch(error => {
                    console.error('Error loading referral chain:', error);
                    alert('Error loading referral chain');
                });
        }

        function editUser(userId) {
            // Redirect to edit user page or show edit modal
            window.location.href = `edit_user.php?user_id=${userId}`;
        }

        // Auto-refresh data every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);

        // Handle tab switching with URL hash
        const triggerTabList = document.querySelectorAll('#adminTabs button');
        triggerTabList.forEach(triggerEl => {
            const tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', event => {
                event.preventDefault();
                tabTrigger.show();
                window.location.hash = triggerEl.getAttribute('data-bs-target').substring(1);
            });
        });

        // Show tab based on URL hash
        if (window.location.hash) {
            const hash = window.location.hash;
            const tabEl = document.querySelector(`#adminTabs button[data-bs-target="${hash}"]`);
            if (tabEl) {
                const tab = new bootstrap.Tab(tabEl);
                tab.show();
            }
        }
    </script>
</body>
</html>
