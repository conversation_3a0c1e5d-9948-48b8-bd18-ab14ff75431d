"""
Test script for the Gift Code System
Verifies all gift code functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from config.database import db_manager
from services.gift_code_service import GiftCodeService
from services.user_service import UserService
from models.gift_code import GiftCodeModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GiftCodeSystemTester:
    """Test the gift code management system"""
    
    def __init__(self):
        self.gift_code_service = GiftCodeService()
        self.user_service = UserService()
    
    async def run_all_tests(self):
        """Run all gift code system tests"""
        try:
            logger.info("Starting Gift Code System Tests...")
            
            # Connect to database
            if not await db_manager.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Run tests
            await self.test_gift_code_models()
            await self.test_gift_code_validation()
            await self.test_gift_code_creation()
            await self.test_gift_code_redemption()
            await self.test_gift_code_management()
            await self.test_message_formatting()
            await self.test_keyboard_generation()
            
            logger.info("All Gift Code System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
        finally:
            await db_manager.disconnect()
    
    async def test_gift_code_models(self):
        """Test gift code model functionality"""
        logger.info("Testing gift code models...")
        
        # Test gift code creation
        code_data = GiftCodeModel.create_gift_code(
            code="TEST123",
            amount=50.0,
            usage_limit=10,
            created_by=999,
            expiry_date=0
        )
        
        assert code_data['code'] == "TEST123", "Code should match"
        assert code_data['amount'] == 50.0, "Amount should match"
        assert code_data['usage_limit'] == 10, "Usage limit should match"
        assert code_data['created_by'] == 999, "Created by should match"
        assert code_data['used_count'] == 0, "Initial used count should be 0"
        assert code_data['redeemed_by'] == [], "Initial redeemed by should be empty"
        
        logger.info("✅ Gift code model creation test passed")
    
    async def test_gift_code_validation(self):
        """Test gift code validation"""
        logger.info("Testing gift code validation...")
        
        # Test valid code format
        validation = GiftCodeModel.validate_gift_code_format("VALID123")
        assert validation['valid'], "Valid code should pass validation"
        
        # Test invalid code format - too short
        validation = GiftCodeModel.validate_gift_code_format("AB")
        assert not validation['valid'], "Too short code should fail validation"
        assert "between 3 and 20 characters" in validation['errors'][0], "Should mention length requirement"
        
        # Test invalid code format - special characters
        validation = GiftCodeModel.validate_gift_code_format("INVALID@123")
        assert not validation['valid'], "Code with special characters should fail validation"
        assert "letters and numbers" in validation['errors'][0], "Should mention format requirement"
        
        # Test valid amount
        validation = GiftCodeModel.validate_gift_amount("50")
        assert validation['valid'], "Valid amount should pass validation"
        assert validation['amount'] == 50.0, "Amount should be converted to float"
        
        # Test invalid amount - negative
        validation = GiftCodeModel.validate_gift_amount("-10")
        assert not validation['valid'], "Negative amount should fail validation"
        
        # Test invalid amount - non-numeric
        validation = GiftCodeModel.validate_gift_amount("abc")
        assert not validation['valid'], "Non-numeric amount should fail validation"
        
        # Test valid usage limit - unlimited
        validation = GiftCodeModel.validate_usage_limit("unlimited")
        assert validation['valid'], "Unlimited should be valid"
        assert validation['usage_limit'] == 0, "Unlimited should be 0"
        
        # Test valid usage limit - number
        validation = GiftCodeModel.validate_usage_limit("5")
        assert validation['valid'], "Numeric limit should be valid"
        assert validation['usage_limit'] == 5, "Limit should be converted to int"
        
        logger.info("✅ Gift code validation test passed")
    
    async def test_gift_code_creation(self):
        """Test gift code creation"""
        logger.info("Testing gift code creation...")
        
        # Create test gift code
        test_code_data = GiftCodeModel.create_gift_code(
            code="TESTCODE123",
            amount=100.0,
            usage_limit=5,
            created_by=999
        )
        
        # Add gift code to database
        success = await self.gift_code_service.add_gift_code(test_code_data)
        assert success, "Gift code creation should succeed"
        
        # Verify code exists
        exists = await self.gift_code_service.check_code_exists("TESTCODE123")
        assert exists, "Gift code should exist after creation"
        
        # Try to create duplicate code (should fail)
        duplicate_success = await self.gift_code_service.add_gift_code(test_code_data)
        assert not duplicate_success, "Duplicate gift code creation should fail"
        
        # Get gift code by code
        retrieved_code = await self.gift_code_service.get_gift_code_by_code("TESTCODE123")
        assert retrieved_code is not None, "Should retrieve gift code"
        assert retrieved_code['code'] == "TESTCODE123", "Retrieved code should match"
        assert retrieved_code['amount'] == 100.0, "Retrieved amount should match"
        
        logger.info("✅ Gift code creation test passed")
    
    async def test_gift_code_redemption(self):
        """Test gift code redemption"""
        logger.info("Testing gift code redemption...")
        
        # Create test user
        test_user_id = 5000
        await self.user_service.create_user(
            test_user_id, "Gift Test User", "", "giftuser", "None"
        )
        
        # Set initial balance
        await self.user_service.update_user_balance(test_user_id, 50, 'set')
        
        # Get initial balance
        user = await self.user_service.get_user(test_user_id)
        initial_balance = user.get('balance', 0)
        
        # Redeem valid gift code
        result = await self.gift_code_service.redeem_gift_code("TESTCODE123", test_user_id)
        assert result['success'], f"Gift code redemption should succeed: {result.get('message', '')}"
        assert result['amount'] == 100.0, "Redeemed amount should match"
        
        # Verify balance update
        updated_user = await self.user_service.get_user(test_user_id)
        new_balance = updated_user.get('balance', 0)
        assert new_balance == initial_balance + 100.0, "Balance should be updated"
        
        # Try to redeem same code again (should fail)
        duplicate_result = await self.gift_code_service.redeem_gift_code("TESTCODE123", test_user_id)
        assert not duplicate_result['success'], "Duplicate redemption should fail"
        assert "already redeemed" in duplicate_result['message'], "Should mention already redeemed"
        
        # Try to redeem invalid code
        invalid_result = await self.gift_code_service.redeem_gift_code("INVALID123", test_user_id)
        assert not invalid_result['success'], "Invalid code redemption should fail"
        assert "Invalid gift code" in invalid_result['message'], "Should mention invalid code"
        
        logger.info("✅ Gift code redemption test passed")
    
    async def test_gift_code_management(self):
        """Test gift code management functions"""
        logger.info("Testing gift code management...")
        
        # Get all gift codes
        all_codes = await self.gift_code_service.get_all_gift_codes()
        assert len(all_codes) > 0, "Should have at least one gift code"
        
        # Get active gift codes
        active_codes = await self.gift_code_service.get_active_gift_codes()
        assert len(active_codes) > 0, "Should have active gift codes"
        
        # Get gift codes by admin
        admin_codes = await self.gift_code_service.get_gift_codes_by_admin(999)
        assert len(admin_codes) > 0, "Should have codes created by admin 999"
        
        # Get statistics
        stats = await self.gift_code_service.get_gift_code_statistics()
        assert stats['total_codes'] > 0, "Should have total codes"
        assert stats['total_redemptions'] > 0, "Should have redemptions"
        assert stats['total_redeemed'] > 0, "Should have redeemed amount"
        
        logger.info("✅ Gift code management test passed")
    
    async def test_message_formatting(self):
        """Test message formatting"""
        logger.info("Testing message formatting...")
        
        # Test generation step 1 message
        step1_message = GiftCodeModel.format_gift_code_generation_step1_message()
        assert "Generate Gift Code" in step1_message, "Should contain title"
        assert "Step 1" in step1_message, "Should contain step number"
        assert "letters and numbers only" in step1_message, "Should contain format instruction"
        
        # Test generation step 2 message
        step2_message = GiftCodeModel.format_gift_code_generation_step2_message("TEST123")
        assert "TEST123" in step2_message, "Should contain code"
        assert "Step 2" in step2_message, "Should contain step number"
        assert "gift amount" in step2_message, "Should mention amount"
        
        # Test generation step 3 message
        step3_message = GiftCodeModel.format_gift_code_generation_step3_message("TEST123", 50.0)
        assert "TEST123" in step3_message, "Should contain code"
        assert "₹50" in step3_message, "Should contain amount"
        assert "Step 3" in step3_message, "Should contain step number"
        assert "usage limit" in step3_message, "Should mention usage limit"
        
        # Test creation success message
        success_message = GiftCodeModel.format_gift_code_creation_success_message("TEST123", 50.0, 10)
        assert "Gift Code Created Successfully!" in success_message, "Should contain success title"
        assert "TEST123" in success_message, "Should contain code"
        assert "₹50" in success_message, "Should contain amount"
        assert "10" in success_message, "Should contain usage limit"
        
        # Test redemption messages
        redeem_message = GiftCodeModel.format_redeem_gift_code_message()
        assert "Redeem Gift Code" in redeem_message, "Should contain title"
        assert "Enter your gift code" in redeem_message, "Should contain instruction"
        
        redemption_success = GiftCodeModel.format_gift_code_redemption_success_message(25.0)
        assert "Gift Code Redeemed Successfully!" in redemption_success, "Should contain success title"
        assert "₹25" in redemption_success, "Should contain amount"
        
        redemption_failure = GiftCodeModel.format_gift_code_redemption_failure_message("Test error")
        assert "Redemption Failed" in redemption_failure, "Should contain failure title"
        assert "Test error" in redemption_failure, "Should contain error message"
        
        logger.info("✅ Message formatting test passed")
    
    async def test_keyboard_generation(self):
        """Test keyboard generation"""
        logger.info("Testing keyboard generation...")
        
        # Test generation success keyboard
        success_keyboard = GiftCodeModel.create_gift_code_generation_success_keyboard()
        assert success_keyboard is not None, "Should generate keyboard"
        assert len(success_keyboard.inline_keyboard) == 2, "Should have 2 rows"
        
        # Check button texts
        button_texts = []
        for row in success_keyboard.inline_keyboard:
            for button in row:
                button_texts.append(button.text)
        
        assert any("Generate Another Code" in text for text in button_texts), "Should have generate button"
        assert any("Back to Admin Panel" in text for text in button_texts), "Should have back button"
        
        # Test redemption success keyboard
        redemption_keyboard = GiftCodeModel.create_gift_code_redemption_success_keyboard()
        assert redemption_keyboard is not None, "Should generate redemption keyboard"
        assert len(redemption_keyboard.inline_keyboard) == 2, "Should have 2 rows"
        
        # Test admin gift codes keyboard
        admin_keyboard = GiftCodeModel.create_admin_gift_codes_keyboard()
        assert admin_keyboard is not None, "Should generate admin keyboard"
        assert len(admin_keyboard.inline_keyboard) == 3, "Should have 3 rows"
        
        logger.info("✅ Keyboard generation test passed")
    
    async def cleanup_test_data(self):
        """Clean up test data (optional)"""
        logger.info("Cleaning up test data...")
        
        # Note: In a real scenario, you might want to clean up test data
        # For now, we'll leave it for manual inspection
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main test function"""
    tester = GiftCodeSystemTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 All Gift Code System tests PASSED!")
            print("The gift code system is ready for production use.")
        else:
            print("\n❌ Some tests FAILED!")
            print("Please check the logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
