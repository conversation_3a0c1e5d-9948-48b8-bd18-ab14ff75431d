"""
Test script for missing features audit
Verifies all missing admin configuration features work correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    BOT_USERNAME = "testbot"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [*********]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

def mock_is_admin(user_id):
    return user_id in [*********]

def mock_get_all_admin_ids():
    return [*********]

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date,
    'is_admin': mock_is_admin,
    'get_all_admin_ids': mock_get_all_admin_ids
})
sys.modules['utils.helpers'] = helpers_module

# Mock database
class MockCollection:
    def __init__(self, collection_name):
        self.collection_name = collection_name
        self.data = {}
    
    async def find_one(self, query):
        if self.collection_name == 'admin_settings':
            return {
                'admin_id': *********,
                'main_channel': 'testchannel',
                'private_logs_channel': '@testlogs',
                'maintenance_status': 'Off',
                'otp_website_api_key': 'test_key',
                'per_refer_amount_range': '20-50',
                'joining_bonus_amount_range': '30-60'
            }
        elif self.collection_name == 'withdrawal_settings':
            return {
                'admin_id': *********,
                'enabled': True,
                'tax_type': 'none',
                'tax_amount': 0,
                'tax_percentage': 0
            }
        elif self.collection_name == 'users':
            user_id = query.get('user_id')
            if user_id == 12345:
                return {
                    'user_id': 12345,
                    'first_name': 'Top User',
                    'username': 'topuser',
                    'promotion_report': [1, 2, 3],
                    'banned': False
                }
            elif user_id == 67890:
                return {
                    'user_id': 67890,
                    'first_name': 'Second User',
                    'username': 'seconduser',
                    'promotion_report': [1, 2],
                    'banned': False
                }
        return None
    
    async def update_one(self, filter_query, update_query, upsert=False):
        class MockResult:
            def __init__(self):
                self.modified_count = 1
                self.upserted_id = None
        return MockResult()
    
    def aggregate(self, pipeline):
        class MockCursor:
            def __init__(self, collection_name):
                self.collection_name = collection_name

            async def to_list(self, length=None):
                if self.collection_name == 'withdrawals':
                    return [
                        {
                            '_id': 12345,
                            'total_withdrawn': 1000.0,
                            'withdrawal_count': 5
                        },
                        {
                            '_id': 67890,
                            'total_withdrawn': 750.0,
                            'withdrawal_count': 3
                        }
                    ]
                elif self.collection_name == 'users':
                    return [
                        {
                            'user_id': 12345,
                            'first_name': 'Top User',
                            'username': 'topuser',
                            'promotion_report': [1, 2, 3],
                            'banned': False
                        },
                        {
                            'user_id': 67890,
                            'first_name': 'Second User',
                            'username': 'seconduser',
                            'promotion_report': [1, 2],
                            'banned': False
                        }
                    ]
                return []

        return MockCursor(self.collection_name)

async def mock_get_collection(name):
    return MockCollection(name)

# Mock database module
database_module = type('MockDatabase', (), {
    'get_collection': mock_get_collection,
    'COLLECTIONS': {
        'admin_settings': 'admin_settings',
        'withdrawal_settings': 'withdrawal_settings',
        'withdrawals': 'withdrawals',
        'users': 'users'
    }
})
sys.modules['config.database'] = database_module

from services.admin_service import AdminService
from services.analytics_service import AnalyticsService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MissingFeaturesTester:
    """Test missing features implementation"""
    
    def __init__(self):
        self.admin_service = AdminService()
        self.analytics_service = AnalyticsService()
    
    async def run_all_tests(self):
        """Run all missing features tests"""
        try:
            logger.info("Starting Missing Features Tests...")
            
            # Test admin configuration features
            await self.test_admin_settings()
            await self.test_withdrawal_settings()
            await self.test_maintenance_mode()
            await self.test_rank_command()
            await self.test_withdrawal_tax_calculation()
            
            logger.info("All Missing Features tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
    
    async def test_admin_settings(self):
        """Test admin settings functionality"""
        logger.info("Testing admin settings...")
        
        # Test getting admin settings
        settings = await self.admin_service.get_admin_settings(*********)
        assert settings is not None, "Should get admin settings"
        assert 'main_channel' in settings, "Should have main_channel"
        assert 'maintenance_status' in settings, "Should have maintenance_status"
        
        # Test updating admin setting
        success = await self.admin_service.update_admin_setting(*********, 'main_channel', 'newchannel')
        assert success, "Should update admin setting successfully"
        
        logger.info("✅ Admin settings test passed")
    
    async def test_withdrawal_settings(self):
        """Test withdrawal settings functionality"""
        logger.info("Testing withdrawal settings...")
        
        # Test getting withdrawal settings
        settings = await self.admin_service.get_withdrawal_settings(*********)
        assert settings is not None, "Should get withdrawal settings"
        assert 'enabled' in settings, "Should have enabled field"
        assert 'tax_type' in settings, "Should have tax_type field"
        
        # Test updating withdrawal settings
        new_settings = {
            'enabled': True,
            'tax_type': 'fixed',
            'tax_amount': 10,
            'tax_percentage': 0
        }
        success = await self.admin_service.update_withdrawal_settings(*********, new_settings)
        assert success, "Should update withdrawal settings successfully"
        
        logger.info("✅ Withdrawal settings test passed")
    
    async def test_maintenance_mode(self):
        """Test maintenance mode toggle"""
        logger.info("Testing maintenance mode...")
        
        # Test toggling maintenance mode
        result = await self.admin_service.toggle_maintenance_mode(*********)
        assert result['success'], "Should toggle maintenance mode successfully"
        assert 'new_status' in result, "Should return new status"
        assert 'message' in result, "Should return message"
        
        logger.info("✅ Maintenance mode test passed")
    
    async def test_rank_command(self):
        """Test rank command functionality"""
        logger.info("Testing rank command...")
        
        # Test getting top users by withdrawals
        top_users = await self.analytics_service.get_top_users_by_withdrawals(15)
        assert isinstance(top_users, list), "Should return list of users"
        
        if top_users:
            user = top_users[0]
            assert 'user_id' in user, "Should have user_id"
            assert 'total_withdrawn' in user, "Should have total_withdrawn"
            assert 'withdrawal_count' in user, "Should have withdrawal_count"
            assert 'first_name' in user, "Should have first_name"
        
        logger.info("✅ Rank command test passed")
    
    async def test_withdrawal_tax_calculation(self):
        """Test withdrawal tax calculation"""
        logger.info("Testing withdrawal tax calculation...")
        
        # Test no tax
        result = await self.admin_service.calculate_withdrawal_tax(100.0, *********)
        assert result['tax_type'] == 'none', "Should have no tax type"
        assert result['final_amount'] == 100.0, "Should have full amount"
        assert result['tax_amount'] == 0, "Should have no tax"
        
        # Test with mock fixed tax
        # First update settings to fixed tax
        tax_settings = {
            'enabled': True,
            'tax_type': 'fixed',
            'tax_amount': 10,
            'tax_percentage': 0
        }
        await self.admin_service.update_withdrawal_settings(*********, tax_settings)
        
        # Mock the get_withdrawal_settings to return fixed tax
        original_method = self.admin_service.get_withdrawal_settings
        
        async def mock_get_withdrawal_settings(admin_id):
            return {
                'enabled': True,
                'tax_type': 'fixed',
                'tax_amount': 10,
                'tax_percentage': 0
            }
        
        self.admin_service.get_withdrawal_settings = mock_get_withdrawal_settings
        
        result = await self.admin_service.calculate_withdrawal_tax(100.0, *********)
        assert result['tax_type'] == 'fixed', "Should have fixed tax type"
        assert result['final_amount'] == 90.0, "Should deduct fixed tax"
        assert result['tax_amount'] == 10, "Should have correct tax amount"
        
        # Restore original method
        self.admin_service.get_withdrawal_settings = original_method
        
        logger.info("✅ Withdrawal tax calculation test passed")

def main():
    """Main test function"""
    async def run_tests():
        tester = MissingFeaturesTester()
        
        try:
            success = await tester.run_all_tests()
            
            if success:
                print("\n🎉 All Missing Features tests PASSED!")
                print("All admin configuration features are working correctly.")
                print("The Python bot now has 100% feature parity with PHP version.")
            else:
                print("\n❌ Some tests FAILED!")
                print("Please check the logs for details.")
                sys.exit(1)
                
        except Exception as e:
            print(f"\n💥 Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(run_tests())

if __name__ == "__main__":
    main()
