"""
Test script for the Level Rewards System
Verifies all level rewards functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from models.level_rewards import LevelRewardsModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LevelRewardsSystemTester:
    """Test the level rewards management system"""
    
    def __init__(self):
        pass

    async def run_all_tests(self):
        """Run all level rewards system tests"""
        try:
            logger.info("Starting Level Rewards System Tests...")

            # Run model tests only (no database required)
            await self.test_level_rewards_models()
            await self.test_message_formatting()
            await self.test_keyboard_generation()

            logger.info("All Level Rewards System tests completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
    
    async def test_level_rewards_models(self):
        """Test level rewards model functionality"""
        logger.info("Testing level rewards models...")
        
        # Test default configuration
        default_config = LevelRewardsModel.get_default_config()
        assert 'referral_requirements' in default_config, "Default config missing referral_requirements"
        assert 'bonus_amounts' in default_config, "Default config missing bonus_amounts"
        assert len(default_config['referral_requirements']) == 6, "Should have 6 referral requirements"
        assert len(default_config['bonus_amounts']) == 6, "Should have 6 bonus amounts"
        
        # Test configuration validation
        valid_config = {
            'referral_requirements': [1, 5, 10, 15, 20, 25],
            'bonus_amounts': [2, 10, 15, 20, 25, 30]
        }
        validation = LevelRewardsModel.validate_config(valid_config)
        assert validation['valid'], f"Valid config should pass validation: {validation['errors']}"
        
        # Test invalid configuration
        invalid_config = {
            'referral_requirements': [1, 5, 10],  # Only 3 levels
            'bonus_amounts': [2, 10, 15, 20, 25, 30]
        }
        validation = LevelRewardsModel.validate_config(invalid_config)
        assert not validation['valid'], "Invalid config should fail validation"
        
        # Test level calculations
        referral_count = 12
        current_level = LevelRewardsModel.calculate_user_current_level(referral_count, default_config)
        assert current_level == 3, f"User with 12 referrals should be level 3, got {current_level}"
        
        # Test eligible levels
        claimed_levels = [1, 2]
        eligible_levels = LevelRewardsModel.get_user_eligible_levels(
            referral_count, claimed_levels, default_config
        )
        assert 3 in eligible_levels, "Level 3 should be eligible"
        assert 1 not in eligible_levels, "Level 1 should not be eligible (already claimed)"
        
        logger.info("✅ Level rewards models test passed")
    
    async def test_message_formatting(self):
        """Test message formatting"""
        logger.info("Testing message formatting...")

        # Test success message formatting
        success_message = LevelRewardsModel.format_level_claim_success_message(1, 10.0, 100.0)
        assert "Level Bonus Claimed Successfully!" in success_message, "Should contain success title"
        assert "Level: 1" in success_message, "Should contain level"
        assert "₹10" in success_message, "Should contain amount"

        # Test admin config message
        config = LevelRewardsModel.get_default_config()
        admin_message = LevelRewardsModel.format_admin_config_message(config, True)
        assert "Level Rewards Configuration" in admin_message, "Should contain title"
        assert "Status:" in admin_message, "Should contain status"
        assert "Current Configuration:" in admin_message, "Should contain configuration"

        logger.info("✅ Message formatting test passed")

    async def test_keyboard_generation(self):
        """Test keyboard generation"""
        logger.info("Testing keyboard generation...")

        # Test admin keyboard generation
        keyboard = LevelRewardsModel.create_admin_config_keyboard(True)
        assert keyboard is not None, "Should generate keyboard"
        assert len(keyboard.inline_keyboard) == 3, "Should have 3 rows"

        # Test level rewards keyboard
        config = LevelRewardsModel.get_default_config()
        eligible_levels = [1, 2]  # Mock eligible levels
        keyboard = LevelRewardsModel.create_level_rewards_keyboard(eligible_levels, config)
        assert keyboard is not None, "Should generate keyboard"
        assert len(keyboard.inline_keyboard) >= 1, "Should have at least back button"

        logger.info("✅ Keyboard generation test passed")
    
    async def cleanup_test_data(self):
        """Clean up test data (optional)"""
        logger.info("Cleaning up test data...")
        
        # Note: In a real scenario, you might want to clean up test data
        # For now, we'll leave it for manual inspection
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main test function"""
    tester = LevelRewardsSystemTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 All Level Rewards System tests PASSED!")
            print("The level rewards system is ready for production use.")
        else:
            print("\n❌ Some tests FAILED!")
            print("Please check the logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
