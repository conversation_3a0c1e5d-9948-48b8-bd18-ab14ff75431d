"""
Test script for the Broadcasting System
Verifies all broadcasting-related functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from config.database import db_manager
from services.admin_service import AdminService
from services.user_service import UserService
from models.broadcast import BroadcastModel, GiftBroadcastModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BroadcastingSystemTester:
    """Test the broadcasting management system"""
    
    def __init__(self):
        self.admin_service = AdminService()
        self.user_service = UserService()
    
    async def run_all_tests(self):
        """Run all broadcasting system tests"""
        try:
            logger.info("Starting Broadcasting System Tests...")
            
            # Connect to database
            if not await db_manager.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Run tests
            await self.test_broadcast_models()
            await self.test_message_broadcast_creation()
            await self.test_gift_broadcast_creation()
            await self.test_broadcast_session_management()
            await self.test_gift_claiming()
            await self.test_channel_parsing()
            
            logger.info("All Broadcasting System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
        finally:
            await db_manager.disconnect()
    
    async def test_broadcast_models(self):
        """Test broadcast model functionality"""
        logger.info("Testing broadcast models...")
        
        # Test text message data creation
        text_data = BroadcastModel.create_text_message_data("Test broadcast message")
        assert text_data['type'] == 'text', "Text message type incorrect"
        assert text_data['text'] == "Test broadcast message", "Text message content incorrect"
        
        # Test photo message data creation
        photo_data = BroadcastModel.create_photo_message_data("photo_file_id", "Test caption")
        assert photo_data['type'] == 'photo', "Photo message type incorrect"
        assert photo_data['photo'] == "photo_file_id", "Photo file ID incorrect"
        assert photo_data['caption'] == "Test caption", "Photo caption incorrect"
        
        # Test broadcast session creation
        target_users = [123, 456, 789]
        session = BroadcastModel.create_broadcast_session(
            admin_id=999,
            broadcast_type="message",
            message_data=text_data,
            target_users=target_users
        )
        
        assert session['admin_id'] == 999, "Admin ID incorrect"
        assert session['type'] == "message", "Broadcast type incorrect"
        assert session['total_users'] == 3, "Total users count incorrect"
        assert session['status'] == "active", "Initial status incorrect"
        
        # Test progress bar creation
        progress_bar = BroadcastModel.create_progress_bar(50.0, 10)
        assert len(progress_bar) == 10, "Progress bar length incorrect"
        assert progress_bar.count('█') == 5, "Progress bar filled count incorrect"
        
        logger.info("✅ Broadcast models test passed")
    
    async def test_message_broadcast_creation(self):
        """Test message broadcast creation"""
        logger.info("Testing message broadcast creation...")
        
        # Create test users
        test_users = []
        for i in range(3):
            user_id = 1000 + i
            await self.user_service.create_user(
                user_id, f"Test User {i}", "", f"testuser{i}", "None"
            )
            test_users.append(user_id)
        
        # Test message broadcast
        message_data = BroadcastModel.create_text_message_data("Test broadcast to users")
        
        result = await self.admin_service.start_message_broadcast(
            admin_id=999,
            message_data=message_data,
            target_users=test_users
        )
        
        assert result['success'], f"Message broadcast should succeed: {result.get('error', '')}"
        assert result['total_users'] == 3, "Target users count incorrect"
        assert 'broadcast_id' in result, "Broadcast ID missing"
        
        # Verify broadcast session was created
        broadcast_id = result['broadcast_id']
        session = await self.admin_service.get_broadcast_session(broadcast_id)
        assert session is not None, "Broadcast session not found"
        assert session['admin_id'] == 999, "Session admin ID incorrect"
        assert session['type'] == "message", "Session type incorrect"
        
        logger.info("✅ Message broadcast creation test passed")
        return broadcast_id
    
    async def test_gift_broadcast_creation(self):
        """Test gift broadcast creation"""
        logger.info("Testing gift broadcast creation...")
        
        # Create test channel data
        channel_data = {
            "type": "public",
            "username": "testchannel",
            "title": "@testchannel",
            "id": "@testchannel",
            "invite_link": "https://t.me/testchannel"
        }
        
        # Test gift broadcast
        result = await self.admin_service.start_gift_broadcast(
            admin_id=999,
            channel="testchannel",
            amount=50.0,
            channel_data=channel_data
        )
        
        assert result['success'], f"Gift broadcast should succeed: {result.get('error', '')}"
        assert 'broadcast_id' in result, "Broadcast ID missing"
        
        # Verify gift broadcast data was stored
        gift_broadcast = await self.admin_service.get_current_gift_broadcast()
        assert gift_broadcast is not None, "Gift broadcast data not found"
        assert gift_broadcast['channel'] == "testchannel", "Channel name incorrect"
        assert gift_broadcast['amount'] == 50.0, "Gift amount incorrect"
        
        # Verify broadcast session was created
        broadcast_id = result['broadcast_id']
        session = await self.admin_service.get_broadcast_session(broadcast_id)
        assert session is not None, "Gift broadcast session not found"
        assert session['type'] == "gift", "Session type incorrect"
        
        logger.info("✅ Gift broadcast creation test passed")
        return broadcast_id
    
    async def test_broadcast_session_management(self):
        """Test broadcast session management"""
        logger.info("Testing broadcast session management...")
        
        # Test getting active broadcast
        active_broadcast = await self.admin_service.get_active_broadcast(999)
        assert active_broadcast is not None, "Should have active broadcast"
        
        # Test updating broadcast session
        broadcast_id = active_broadcast['broadcast_id']
        update_data = {
            'sent_count': 5,
            'failed_count': 1,
            'status': 'completed'
        }
        
        success = await self.admin_service.update_broadcast_session(broadcast_id, update_data)
        assert success, "Broadcast session update should succeed"
        
        # Verify update
        updated_session = await self.admin_service.get_broadcast_session(broadcast_id)
        assert updated_session['sent_count'] == 5, "Sent count not updated"
        assert updated_session['failed_count'] == 1, "Failed count not updated"
        assert updated_session['status'] == 'completed', "Status not updated"
        
        logger.info("✅ Broadcast session management test passed")
    
    async def test_gift_claiming(self):
        """Test gift claiming functionality"""
        logger.info("Testing gift claiming...")
        
        # Create test user
        test_user_id = 2000
        await self.user_service.create_user(
            test_user_id, "Gift Test User", "", "giftuser", "None"
        )
        
        # Set initial balance
        await self.user_service.update_user_balance(test_user_id, 100, 'set')
        
        # Create gift broadcast data
        gift_data = GiftBroadcastModel.create_gift_broadcast_data(
            channel="testchannel",
            amount=25.0,
            channel_data={
                "type": "public",
                "username": "testchannel",
                "title": "@testchannel"
            }
        )
        
        # Set current gift broadcast
        await self.admin_service.set_current_gift_broadcast(gift_data)
        
        # Test gift claiming
        gift_id = gift_data['broadcast_id']
        
        # Add gift to user's claimed gifts
        success = await self.user_service.add_claimed_gift(test_user_id, gift_id)
        assert success, "Adding claimed gift should succeed"
        
        # Verify gift was added
        user = await self.user_service.get_user(test_user_id)
        claimed_gifts = user.get('claimed_gifts', [])
        assert gift_id in claimed_gifts, "Gift ID should be in claimed gifts"
        
        logger.info("✅ Gift claiming test passed")
    
    async def test_channel_parsing(self):
        """Test channel input parsing"""
        logger.info("Testing channel parsing...")
        
        # Import the session handlers to test channel parsing
        from handlers.session_handlers import SessionHandlers
        session_handlers = SessionHandlers()
        
        # Test username format
        channel_data = await session_handlers._parse_channel_input("@testchannel")
        assert channel_data is not None, "Username parsing should succeed"
        assert channel_data['type'] == 'public', "Channel type should be public"
        assert channel_data['username'] == 'testchannel', "Username incorrect"
        
        # Test URL format
        channel_data = await session_handlers._parse_channel_input("https://t.me/testchannel")
        assert channel_data is not None, "URL parsing should succeed"
        assert channel_data['username'] == 'testchannel', "Username from URL incorrect"
        
        # Test private invite link
        channel_data = await session_handlers._parse_channel_input("https://t.me/+privateinvite")
        assert channel_data is not None, "Private invite parsing should succeed"
        assert channel_data['type'] == 'private', "Private channel type incorrect"
        
        logger.info("✅ Channel parsing test passed")
    
    async def test_gift_message_formatting(self):
        """Test gift message formatting"""
        logger.info("Testing gift message formatting...")
        
        # Test gift message creation
        gift_message = GiftBroadcastModel.format_gift_message(
            channel_title="@TestChannel",
            amount=100.0,
            join_link="https://t.me/testchannel",
            join_text="Join Channel"
        )
        
        assert "₹100" in gift_message, "Amount should be in message"
        assert "@TestChannel" in gift_message, "Channel title should be in message"
        assert "Free Money Alert" in gift_message, "Alert text should be in message"
        
        # Test gift keyboard creation
        keyboard = GiftBroadcastModel.create_gift_keyboard(
            join_link="https://t.me/testchannel",
            join_text="Join Channel"
        )
        
        assert keyboard is not None, "Keyboard should be created"
        assert len(keyboard.inline_keyboard) == 2, "Should have 2 rows"
        assert len(keyboard.inline_keyboard[0]) == 1, "First row should have 1 button"
        assert len(keyboard.inline_keyboard[1]) == 1, "Second row should have 1 button"
        
        logger.info("✅ Gift message formatting test passed")
    
    async def cleanup_test_data(self):
        """Clean up test data (optional)"""
        logger.info("Cleaning up test data...")
        
        # Note: In a real scenario, you might want to clean up test data
        # For now, we'll leave it for manual inspection
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main test function"""
    tester = BroadcastingSystemTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 All Broadcasting System tests PASSED!")
            print("The broadcasting system is ready for production use.")
        else:
            print("\n❌ Some tests FAILED!")
            print("Please check the logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
