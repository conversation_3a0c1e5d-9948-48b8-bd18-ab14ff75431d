"""
Test script for Admin Features
Verifies admin user management and force subscription functionality
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [123456789]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

def mock_is_admin(user_id):
    return user_id in [123456789, 999]

def mock_send_safe_message(bot, user_id, message, **kwargs):
    print(f"Mock message to {user_id}: {message}")
    return True

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date,
    'is_admin': mock_is_admin,
    'send_safe_message': mock_send_safe_message
})
sys.modules['utils.helpers'] = helpers_module

# Mock telegram bot
class MockBot:
    def __init__(self, token):
        self.token = token

# Mock database
class MockCollection:
    def __init__(self):
        self.data = []

    async def find_one(self, query):
        return None

    async def update_one(self, query, update):
        class MockResult:
            modified_count = 1
        return MockResult()

    async def insert_one(self, data):
        class MockResult:
            inserted_id = "mock_id"
        return MockResult()

    def find(self, query=None):
        class MockCursor:
            async def to_list(self, length=None):
                return []
        return MockCursor()

    async def delete_one(self, query):
        class MockResult:
            deleted_count = 1
        return MockResult()

async def mock_get_collection(name):
    return MockCollection()

# Mock database module
database_module = type('MockDatabase', (), {
    'get_collection': mock_get_collection,
    'COLLECTIONS': {
        'users': 'users',
        'admin_logs': 'admin_logs',
        'force_channels': 'force_channels'
    }
})
sys.modules['config.database'] = database_module

# Mock user service
class MockUserService:
    async def get_user(self, user_id):
        return {
            'user_id': user_id,
            'first_name': 'Test User',
            'username': 'testuser',
            'balance': 100.0,
            'banned': False,
            'created_at': mock_get_current_timestamp(),
            'promotion_report': [],
            'withdrawal_report': []
        }
    
    async def update_user_balance(self, user_id, amount, operation):
        return True

# Mock task service
class MockTaskService:
    async def get_user_submissions(self, user_id):
        return []

sys.modules['services.user_service'] = type('MockModule', (), {'UserService': MockUserService})
sys.modules['services.task_service'] = type('MockModule', (), {'TaskService': MockTaskService})

from services.admin_user_service import AdminUserService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdminFeaturesTester:
    """Test the admin features system"""
    
    def __init__(self):
        self.admin_user_service = AdminUserService()
    
    async def run_all_tests(self):
        """Run all admin features tests"""
        try:
            logger.info("Starting Admin Features Tests...")
            
            # Run tests
            await self.test_add_user_balance()
            await self.test_remove_user_balance()
            await self.test_ban_user()
            await self.test_unban_user()
            await self.test_get_user_record()
            await self.test_force_subscription_service()
            
            logger.info("All Admin Features tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
    
    async def test_add_user_balance(self):
        """Test add user balance functionality"""
        logger.info("Testing add user balance...")
        
        # Test valid balance addition
        result = await self.admin_user_service.add_user_balance(12345, 50.0, 999)
        assert result['success'], "Balance addition should succeed"
        assert "Successfully added ₹50" in result['message'], "Should contain success message"
        assert result['new_balance'] == 150.0, "New balance should be correct"
        
        logger.info("✅ Add user balance test passed")
    
    async def test_remove_user_balance(self):
        """Test remove user balance functionality"""
        logger.info("Testing remove user balance...")
        
        # Test valid balance removal
        result = await self.admin_user_service.remove_user_balance(12345, 25.0, 999)
        assert result['success'], "Balance removal should succeed"
        assert "Successfully removed ₹25" in result['message'], "Should contain success message"
        assert result['new_balance'] == 75.0, "New balance should be correct"
        
        # Test insufficient balance
        result = await self.admin_user_service.remove_user_balance(12345, 200.0, 999)
        assert not result['success'], "Should fail with insufficient balance"
        assert "Insufficient balance" in result['message'], "Should mention insufficient balance"
        
        logger.info("✅ Remove user balance test passed")
    
    async def test_ban_user(self):
        """Test ban user functionality"""
        logger.info("Testing ban user...")
        
        # Test valid user ban
        result = await self.admin_user_service.ban_user(12345, 999, "Test ban reason")
        assert result['success'], "User ban should succeed"
        assert "Successfully banned" in result['message'], "Should contain success message"
        assert result['user_name'] == "Test User", "Should return user name"
        
        logger.info("✅ Ban user test passed")
    
    async def test_unban_user(self):
        """Test unban user functionality"""
        logger.info("Testing unban user...")

        # Create a new mock user service that returns banned user
        class MockBannedUserService:
            async def get_user(self, user_id):
                return {
                    'user_id': user_id,
                    'first_name': 'Test User',
                    'username': 'testuser',
                    'balance': 100.0,
                    'banned': True,  # User is banned
                    'created_at': mock_get_current_timestamp(),
                    'promotion_report': [],
                    'withdrawal_report': []
                }

        # Temporarily replace the user service
        original_user_service = self.admin_user_service.user_service
        self.admin_user_service.user_service = MockBannedUserService()

        # Test valid user unban
        result = await self.admin_user_service.unban_user(12345, 999)
        assert result['success'], f"User unban should succeed, got: {result}"
        assert "Successfully unbanned" in result['message'], "Should contain success message"
        assert result['user_name'] == "Test User", "Should return user name"

        # Restore original user service
        self.admin_user_service.user_service = original_user_service

        logger.info("✅ Unban user test passed")
    
    async def test_get_user_record(self):
        """Test get user record functionality"""
        logger.info("Testing get user record...")
        
        # Test valid user record
        result = await self.admin_user_service.get_user_record(12345)
        assert result['success'], "Get user record should succeed"
        assert 'user' in result, "Should contain user data"
        assert 'statistics' in result, "Should contain statistics"
        
        user_data = result['user']
        stats = result['statistics']
        
        assert user_data['user_id'] == 12345, "Should have correct user ID"
        assert user_data['first_name'] == "Test User", "Should have correct name"
        assert stats['referral_count'] == 0, "Should have referral count"
        assert stats['total_withdrawals'] == 0, "Should have withdrawal count"
        
        logger.info("✅ Get user record test passed")
    
    async def test_force_subscription_service(self):
        """Test force subscription service functionality"""
        logger.info("Testing force subscription service...")
        
        from services.force_subscription_service import ForceSubscriptionService
        force_sub_service = ForceSubscriptionService()
        
        # Test get channels (empty initially)
        channels = await force_sub_service.get_force_subscription_channels()
        assert isinstance(channels, list), "Should return list of channels"
        
        # Test add channel
        channel_data = {
            'channel_id': '-1001234567890',
            'title': 'Test Channel',
            'username': 'testchannel',
            'type': 'channel',
            'added_by': 999
        }
        
        result = await force_sub_service.add_force_subscription_channel(channel_data)
        assert result, "Should successfully add channel"
        
        # Test remove channel
        result = await force_sub_service.remove_force_subscription_channel('-1001234567890')
        assert result, "Should successfully remove channel"
        
        # Test format channels list message
        message = await force_sub_service.format_channels_list_message()
        assert "Force Subscription Channels" in message, "Should contain title"
        
        # Test create management keyboard
        keyboard = await force_sub_service.create_channels_management_keyboard()
        assert keyboard is not None, "Should create keyboard"
        
        logger.info("✅ Force subscription service test passed")

def main():
    """Main test function"""
    async def run_tests():
        tester = AdminFeaturesTester()
        
        try:
            success = await tester.run_all_tests()
            
            if success:
                print("\n🎉 All Admin Features tests PASSED!")
                print("The admin features are ready for production use.")
            else:
                print("\n❌ Some tests FAILED!")
                print("Please check the logs for details.")
                sys.exit(1)
                
        except Exception as e:
            print(f"\n💥 Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(run_tests())

if __name__ == "__main__":
    main()
