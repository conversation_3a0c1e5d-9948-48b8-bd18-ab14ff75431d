"""
Standalone test for Gift Code Models
Tests only the model functionality without database dependencies
"""

import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

# Mock the settings to avoid configuration errors
class MockSettings:
    BOT_TOKEN = "mock_token"
    DATABASE_URL = "mock_url"
    DATABASE_NAME = "mock_db"
    LOG_LEVEL = "INFO"
    LOG_FILE = "mock.log"
    BANNED_TEXT = "You are banned"
    MAINTENANCE_TEXT = "Under maintenance"
    ADMIN_IDS = [123456789]

sys.modules['config.settings'] = type('MockModule', (), {'settings': MockSettings()})

# Mock helpers to avoid circular imports
def mock_get_current_timestamp():
    import time
    return int(time.time())

def mock_get_current_date():
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d')

# Create mock helpers module
helpers_module = type('MockHelpers', (), {
    'get_current_timestamp': mock_get_current_timestamp,
    'get_current_date': mock_get_current_date
})
sys.modules['utils.helpers'] = helpers_module

from models.gift_code import GiftCodeModel

def test_gift_code_models():
    """Test gift code model functionality"""
    print("Testing gift code models...")
    
    # Test gift code creation
    code_data = GiftCodeModel.create_gift_code(
        code="TEST123",
        amount=50.0,
        usage_limit=10,
        created_by=999,
        expiry_date=0
    )
    
    assert code_data['code'] == "TEST123", "Code should match"
    assert code_data['amount'] == 50.0, "Amount should match"
    assert code_data['usage_limit'] == 10, "Usage limit should match"
    assert code_data['created_by'] == 999, "Created by should match"
    assert code_data['used_count'] == 0, "Initial used count should be 0"
    assert code_data['redeemed_by'] == [], "Initial redeemed by should be empty"
    print("✅ Gift code creation test passed")
    
    print("✅ All gift code models tests passed!")

def test_gift_code_validation():
    """Test gift code validation"""
    print("Testing gift code validation...")
    
    # Test valid code format
    validation = GiftCodeModel.validate_gift_code_format("VALID123")
    assert validation['valid'], "Valid code should pass validation"
    print("✅ Valid code format test passed")
    
    # Test invalid code format - too short
    validation = GiftCodeModel.validate_gift_code_format("AB")
    assert not validation['valid'], "Too short code should fail validation"
    assert "between 3 and 20 characters" in validation['errors'][0], "Should mention length requirement"
    print("✅ Invalid code format (too short) test passed")
    
    # Test invalid code format - special characters
    validation = GiftCodeModel.validate_gift_code_format("INVALID@123")
    assert not validation['valid'], "Code with special characters should fail validation"
    assert "letters and numbers" in validation['errors'][0], "Should mention format requirement"
    print("✅ Invalid code format (special chars) test passed")
    
    # Test valid amount
    validation = GiftCodeModel.validate_gift_amount("50")
    assert validation['valid'], "Valid amount should pass validation"
    assert validation['amount'] == 50.0, "Amount should be converted to float"
    print("✅ Valid amount test passed")
    
    # Test invalid amount - negative
    validation = GiftCodeModel.validate_gift_amount("-10")
    assert not validation['valid'], "Negative amount should fail validation"
    print("✅ Invalid amount (negative) test passed")
    
    # Test invalid amount - non-numeric
    validation = GiftCodeModel.validate_gift_amount("abc")
    assert not validation['valid'], "Non-numeric amount should fail validation"
    print("✅ Invalid amount (non-numeric) test passed")
    
    # Test valid usage limit - unlimited
    validation = GiftCodeModel.validate_usage_limit("unlimited")
    assert validation['valid'], "Unlimited should be valid"
    assert validation['usage_limit'] == 0, "Unlimited should be 0"
    print("✅ Valid usage limit (unlimited) test passed")
    
    # Test valid usage limit - number
    validation = GiftCodeModel.validate_usage_limit("5")
    assert validation['valid'], "Numeric limit should be valid"
    assert validation['usage_limit'] == 5, "Limit should be converted to int"
    print("✅ Valid usage limit (numeric) test passed")
    
    print("✅ All gift code validation tests passed!")

def test_redemption_eligibility():
    """Test redemption eligibility checking"""
    print("Testing redemption eligibility...")
    
    # Create test gift code
    gift_code = {
        'code': 'TEST123',
        'amount': 50.0,
        'usage_limit': 3,
        'used_count': 1,
        'redeemed_by': [123],
        'expiry_date': 0
    }
    
    # Test eligible user
    eligibility = GiftCodeModel.check_code_redemption_eligibility(gift_code, 456)
    assert eligibility['eligible'], "New user should be eligible"
    print("✅ Eligible user test passed")
    
    # Test already redeemed user
    eligibility = GiftCodeModel.check_code_redemption_eligibility(gift_code, 123)
    assert not eligibility['eligible'], "User who already redeemed should not be eligible"
    assert "already redeemed" in eligibility['message'], "Should mention already redeemed"
    print("✅ Already redeemed user test passed")
    
    # Test usage limit reached
    exhausted_code = gift_code.copy()
    exhausted_code['used_count'] = 3  # Reached limit
    eligibility = GiftCodeModel.check_code_redemption_eligibility(exhausted_code, 789)
    assert not eligibility['eligible'], "Should not be eligible when usage limit reached"
    assert "usage limit reached" in eligibility['message'], "Should mention usage limit"
    print("✅ Usage limit reached test passed")
    
    # Test expired code
    import time
    expired_code = gift_code.copy()
    expired_code['expiry_date'] = int(time.time()) - 3600  # Expired 1 hour ago
    eligibility = GiftCodeModel.check_code_redemption_eligibility(expired_code, 789)
    assert not eligibility['eligible'], "Should not be eligible when code is expired"
    assert "expired" in eligibility['message'], "Should mention expired"
    print("✅ Expired code test passed")
    
    print("✅ All redemption eligibility tests passed!")

def test_message_formatting():
    """Test message formatting"""
    print("Testing message formatting...")
    
    # Test generation step 1 message
    step1_message = GiftCodeModel.format_gift_code_generation_step1_message()
    assert "Generate Gift Code" in step1_message, "Should contain title"
    assert "Step 1" in step1_message, "Should contain step number"
    assert "letters and numbers only" in step1_message, "Should contain format instruction"
    print("✅ Generation step 1 message test passed")
    
    # Test generation step 2 message
    step2_message = GiftCodeModel.format_gift_code_generation_step2_message("TEST123")
    assert "TEST123" in step2_message, "Should contain code"
    assert "Step 2" in step2_message, "Should contain step number"
    assert "gift amount" in step2_message, "Should mention amount"
    print("✅ Generation step 2 message test passed")
    
    # Test generation step 3 message
    step3_message = GiftCodeModel.format_gift_code_generation_step3_message("TEST123", 50.0)
    assert "TEST123" in step3_message, "Should contain code"
    assert "₹50" in step3_message, "Should contain amount"
    assert "Step 3" in step3_message, "Should contain step number"
    assert "usage limit" in step3_message, "Should mention usage limit"
    print("✅ Generation step 3 message test passed")
    
    # Test creation success message
    success_message = GiftCodeModel.format_gift_code_creation_success_message("TEST123", 50.0, 10)
    assert "Gift Code Created Successfully!" in success_message, "Should contain success title"
    assert "TEST123" in success_message, "Should contain code"
    assert "₹50" in success_message, "Should contain amount"
    assert "10" in success_message, "Should contain usage limit"
    print("✅ Creation success message test passed")
    
    # Test redemption messages
    redeem_message = GiftCodeModel.format_redeem_gift_code_message()
    assert "Redeem Gift Code" in redeem_message, "Should contain title"
    assert "Enter your gift code" in redeem_message, "Should contain instruction"
    print("✅ Redeem message test passed")
    
    redemption_success = GiftCodeModel.format_gift_code_redemption_success_message(25.0)
    assert "Gift Code Redeemed Successfully!" in redemption_success, "Should contain success title"
    assert "₹25" in redemption_success, "Should contain amount"
    print("✅ Redemption success message test passed")
    
    redemption_failure = GiftCodeModel.format_gift_code_redemption_failure_message("Test error")
    assert "Redemption Failed" in redemption_failure, "Should contain failure title"
    assert "Test error" in redemption_failure, "Should contain error message"
    print("✅ Redemption failure message test passed")
    
    print("✅ All message formatting tests passed!")

def test_keyboard_generation():
    """Test keyboard generation"""
    print("Testing keyboard generation...")
    
    # Mock telegram classes
    class MockInlineKeyboardButton:
        def __init__(self, text, callback_data):
            self.text = text
            self.callback_data = callback_data
    
    class MockInlineKeyboardMarkup:
        def __init__(self, keyboard):
            self.inline_keyboard = keyboard
    
    # Monkey patch telegram imports
    import sys
    telegram_module = type('MockTelegram', (), {
        'InlineKeyboardButton': MockInlineKeyboardButton,
        'InlineKeyboardMarkup': MockInlineKeyboardMarkup
    })
    sys.modules['telegram'] = telegram_module
    
    # Re-import the model to get the updated telegram classes
    import importlib
    import models.gift_code
    importlib.reload(models.gift_code)
    from models.gift_code import GiftCodeModel
    
    # Test generation success keyboard
    success_keyboard = GiftCodeModel.create_gift_code_generation_success_keyboard()
    assert success_keyboard is not None, "Should generate keyboard"
    assert len(success_keyboard.inline_keyboard) == 2, "Should have 2 rows"
    
    # Check button texts
    button_texts = []
    for row in success_keyboard.inline_keyboard:
        for button in row:
            button_texts.append(button.text)
    
    assert any("Generate Another Code" in text for text in button_texts), "Should have generate button"
    assert any("Back to Admin Panel" in text for text in button_texts), "Should have back button"
    print("✅ Generation success keyboard test passed")
    
    # Test redemption success keyboard
    redemption_keyboard = GiftCodeModel.create_gift_code_redemption_success_keyboard()
    assert redemption_keyboard is not None, "Should generate redemption keyboard"
    assert len(redemption_keyboard.inline_keyboard) == 2, "Should have 2 rows"
    print("✅ Redemption success keyboard test passed")
    
    # Test admin gift codes keyboard
    admin_keyboard = GiftCodeModel.create_admin_gift_codes_keyboard()
    assert admin_keyboard is not None, "Should generate admin keyboard"
    assert len(admin_keyboard.inline_keyboard) == 3, "Should have 3 rows"
    print("✅ Admin gift codes keyboard test passed")
    
    print("✅ All keyboard generation tests passed!")

def test_statistics():
    """Test statistics calculation"""
    print("Testing statistics...")
    
    # Create test gift codes
    import time
    current_time = int(time.time())
    
    gift_codes = [
        {
            'code': 'ACTIVE1',
            'amount': 50.0,
            'usage_limit': 10,
            'used_count': 3,
            'expiry_date': 0
        },
        {
            'code': 'EXPIRED1',
            'amount': 25.0,
            'usage_limit': 5,
            'used_count': 2,
            'expiry_date': current_time - 3600  # Expired 1 hour ago
        },
        {
            'code': 'EXHAUSTED1',
            'amount': 100.0,
            'usage_limit': 2,
            'used_count': 2,
            'expiry_date': 0
        }
    ]
    
    stats = GiftCodeModel.get_gift_code_statistics(gift_codes)
    
    assert stats['total_codes'] == 3, "Should have 3 total codes"
    assert stats['active_codes'] == 1, "Should have 1 active code"
    assert stats['expired_codes'] == 1, "Should have 1 expired code"
    assert stats['exhausted_codes'] == 1, "Should have 1 exhausted code"
    assert stats['total_amount'] == 175.0, "Total amount should be 175"
    expected_redeemed = 50.0*3 + 25.0*2 + 100.0*2  # 150 + 50 + 200 = 400
    assert stats['total_redeemed'] == expected_redeemed, f"Total redeemed should be {expected_redeemed}, got {stats['total_redeemed']}"
    assert stats['total_redemptions'] == 7, "Total redemptions should be 7 (3+2+2)"
    print("✅ Statistics calculation test passed")
    
    print("✅ All statistics tests passed!")

def main():
    """Main test function"""
    try:
        print("🧪 Starting Gift Code Models Tests...")
        print("=" * 50)
        
        test_gift_code_models()
        print()
        test_gift_code_validation()
        print()
        test_redemption_eligibility()
        print()
        test_message_formatting()
        print()
        test_keyboard_generation()
        print()
        test_statistics()
        
        print("=" * 50)
        print("🎉 All Gift Code Models tests PASSED!")
        print("The gift code models are working correctly.")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
