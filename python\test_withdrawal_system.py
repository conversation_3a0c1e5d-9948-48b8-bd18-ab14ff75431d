"""
Test script for the Withdrawal System
Verifies all withdrawal-related functionality works correctly
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from config.database import db_manager
from services.withdrawal_service import WithdrawalService
from services.user_service import UserService
from models.withdrawal import WithdrawalModel, AccountInfoModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WithdrawalSystemTester:
    """Test the withdrawal management system"""
    
    def __init__(self):
        self.withdrawal_service = WithdrawalService()
        self.user_service = UserService()
    
    async def run_all_tests(self):
        """Run all withdrawal system tests"""
        try:
            logger.info("Starting Withdrawal System Tests...")
            
            # Connect to database
            if not await db_manager.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Run tests
            await self.test_account_info_validation()
            await self.test_withdrawal_amount_validation()
            await self.test_withdrawal_request_creation()
            await self.test_withdrawal_approval()
            await self.test_withdrawal_rejection()
            await self.test_withdrawal_statistics()
            await self.test_account_info_update()
            
            logger.info("All Withdrawal System tests completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            return False
        finally:
            await db_manager.disconnect()
    
    async def test_account_info_validation(self):
        """Test account information validation"""
        logger.info("Testing account info validation...")
        
        # Test bank account validation
        bank_account_complete = {
            'name': 'John Doe',
            'ifsc': 'SBIN0001234',
            'email': '<EMAIL>',
            'account_number': '*********0',
            'mobile_number': '+************',
            'withdrawal_method': 'bank'
        }
        
        bank_validation = AccountInfoModel.validate_bank_account_info(bank_account_complete)
        assert bank_validation['valid'], "Complete bank account should be valid"
        
        # Test incomplete bank account
        bank_account_incomplete = {
            'name': 'John Doe',
            'ifsc': '',
            'email': '<EMAIL>',
            'withdrawal_method': 'bank'
        }
        
        bank_validation_incomplete = AccountInfoModel.validate_bank_account_info(bank_account_incomplete)
        assert not bank_validation_incomplete['valid'], "Incomplete bank account should be invalid"
        assert 'ifsc' in bank_validation_incomplete['missing_fields'], "Missing IFSC should be detected"
        
        # Test USDT account validation
        usdt_account_complete = {
            'binance_id': '*********',
            'withdrawal_method': 'usdt'
        }
        
        usdt_validation = AccountInfoModel.validate_usdt_account_info(usdt_account_complete)
        assert usdt_validation['valid'], "Complete USDT account should be valid"
        
        # Test incomplete USDT account
        usdt_account_incomplete = {
            'binance_id': '',
            'withdrawal_method': 'usdt'
        }
        
        usdt_validation_incomplete = AccountInfoModel.validate_usdt_account_info(usdt_account_incomplete)
        assert not usdt_validation_incomplete['valid'], "Incomplete USDT account should be invalid"
        
        logger.info("✅ Account info validation test passed")
    
    async def test_withdrawal_amount_validation(self):
        """Test withdrawal amount validation"""
        logger.info("Testing withdrawal amount validation...")
        
        # Test valid amount
        validation = WithdrawalModel.validate_withdrawal_amount(500, 1000, 100)
        assert validation['valid'], "Valid withdrawal amount should pass"
        
        # Test insufficient balance
        validation = WithdrawalModel.validate_withdrawal_amount(1500, 1000, 100)
        assert not validation['valid'], "Insufficient balance should fail"
        assert "Insufficient balance" in validation['errors'][0], "Should detect insufficient balance"
        
        # Test below minimum
        validation = WithdrawalModel.validate_withdrawal_amount(50, 1000, 100)
        assert not validation['valid'], "Below minimum should fail"
        assert "Minimum withdrawal amount" in validation['errors'][0], "Should detect minimum amount violation"
        
        logger.info("✅ Withdrawal amount validation test passed")
    
    async def test_withdrawal_request_creation(self):
        """Test withdrawal request creation"""
        logger.info("Testing withdrawal request creation...")
        
        # Create test user with sufficient balance and account info
        test_user_id = *********
        
        # Create user if not exists
        existing_user = await self.user_service.get_user(test_user_id)
        if not existing_user:
            await self.user_service.create_user(
                test_user_id, "Test User", "", "testuser", "None"
            )
        
        # Set user balance
        await self.user_service.update_user_balance(test_user_id, 1000, 'set')
        
        # Set account info
        await self.user_service.update_account_info(test_user_id, 'withdrawal_method', 'bank')
        await self.user_service.update_account_info(test_user_id, 'name', 'Test User')
        await self.user_service.update_account_info(test_user_id, 'ifsc', 'SBIN0001234')
        await self.user_service.update_account_info(test_user_id, 'email', '<EMAIL>')
        await self.user_service.update_account_info(test_user_id, 'account_number', '*********0')
        await self.user_service.update_account_info(test_user_id, 'mobile_number', '+************')
        
        # Test withdrawal request
        result = await self.withdrawal_service.process_withdrawal_request(
            test_user_id, 200, 'bank'
        )
        
        assert result['success'], f"Withdrawal request should succeed: {result.get('error', '')}"
        
        # Verify user balance was deducted
        user = await self.user_service.get_user(test_user_id)
        assert user['balance'] == 800, "Balance should be deducted"
        assert user['withdraw_under_review'] == 200, "Amount should be under review"
        
        logger.info("✅ Withdrawal request creation test passed")
        return test_user_id
    
    async def test_withdrawal_approval(self):
        """Test withdrawal approval"""
        logger.info("Testing withdrawal approval...")
        
        # Use the test user from previous test
        test_user_id = *********
        admin_id = *********
        
        # Approve withdrawal
        success = await self.withdrawal_service.approve_withdrawal(test_user_id, admin_id)
        assert success, "Withdrawal approval should succeed"
        
        # Verify user data updated
        user = await self.user_service.get_user(test_user_id)
        assert user['withdraw_under_review'] == 0, "Under review amount should be cleared"
        assert user['successful_withdraw'] == 200, "Successful withdrawal should be recorded"
        
        logger.info("✅ Withdrawal approval test passed")
    
    async def test_withdrawal_rejection(self):
        """Test withdrawal rejection"""
        logger.info("Testing withdrawal rejection...")
        
        # Create another withdrawal request
        test_user_id = *********
        admin_id = *********
        
        # Create another withdrawal request
        result = await self.withdrawal_service.process_withdrawal_request(
            test_user_id, 300, 'bank'
        )
        assert result['success'], "Second withdrawal request should succeed"
        
        # Reject withdrawal
        success = await self.withdrawal_service.reject_withdrawal(test_user_id, admin_id)
        assert success, "Withdrawal rejection should succeed"
        
        # Verify user data updated
        user = await self.user_service.get_user(test_user_id)
        assert user['withdraw_under_review'] == 0, "Under review amount should be cleared"
        assert user['balance'] == 800, "Balance should be restored"  # 500 remaining + 300 returned
        
        logger.info("✅ Withdrawal rejection test passed")
    
    async def test_withdrawal_statistics(self):
        """Test withdrawal statistics"""
        logger.info("Testing withdrawal statistics...")
        
        stats = await self.withdrawal_service.get_withdrawal_statistics()
        
        # Verify statistics structure
        required_fields = [
            'total_successful_withdrawals', 'total_pending_withdrawals',
            'users_with_withdrawals', 'users_with_pending'
        ]
        
        for field in required_fields:
            assert field in stats, f"Missing statistics field: {field}"
            assert isinstance(stats[field], int), f"Statistics field {field} is not an integer"
        
        logger.info("✅ Withdrawal statistics test passed")
        logger.info(f"Statistics: {stats}")
    
    async def test_account_info_update(self):
        """Test account information update"""
        logger.info("Testing account info update...")
        
        test_user_id = *********
        
        # Test updating various fields
        test_updates = [
            ('name', 'Updated Name'),
            ('email', '<EMAIL>'),
            ('ifsc', 'HDFC0001234'),
            ('account_number', '**********'),
            ('mobile_number', '+919*********'),
            ('withdrawal_method', 'usdt'),
            ('binance_id', 'updated_binance_123')
        ]
        
        for field, value in test_updates:
            success = await self.withdrawal_service.update_account_info(test_user_id, field, value)
            assert success, f"Failed to update {field}"
            
            # Verify update
            user = await self.user_service.get_user(test_user_id)
            account_info = user.get('account_info', {})
            assert account_info.get(field) == value, f"Field {field} not updated correctly"
        
        logger.info("✅ Account info update test passed")
    
    async def cleanup_test_data(self):
        """Clean up test data (optional)"""
        logger.info("Cleaning up test data...")
        
        # Note: In a real scenario, you might want to clean up test data
        # For now, we'll leave it for manual inspection
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main test function"""
    tester = WithdrawalSystemTester()
    
    try:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 All Withdrawal System tests PASSED!")
            print("The withdrawal system is ready for production use.")
        else:
            print("\n❌ Some tests FAILED!")
            print("Please check the logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
